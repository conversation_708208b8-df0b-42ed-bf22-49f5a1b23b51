/**
 * 主应用程序
 * 版本: v3.0.0
 * 创建日期: 2025-07-19
 * 最后更新: 2025-07-30
 * 变更记录:
 * - v1.0.0: 初始版本，实现文件上传、数据预览和基础交互功能
 * - v3.0.0: 代码清理和优化，删除调试代码，提升生产环境性能
 */

class StatisticsApp {
    constructor() {
        this.initializeElements();
        this.bindEvents();
        this.currentData = null;
        this.selectedVariables = {
            general: [], // 一般分析选择的变量
            xVariables: [], // 回归分析自变量
            yVariables: []  // 回归分析因变量
        };
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 文件上传相关元素
        this.fileDropZone = document.getElementById('file-drop-zone');
        this.fileInput = document.getElementById('file-input');
        this.fileSelectBtn = document.getElementById('file-select-btn');
        this.fileInfo = document.getElementById('file-info');
        this.fileName = document.getElementById('file-name');
        this.fileDetails = document.getElementById('file-details');

        // 数据概览相关元素
        this.dataOverview = document.getElementById('data-overview');
        this.columnInfo = document.getElementById('column-info');
        this.dataTableContainer = document.getElementById('data-table-container');

        // 变量选择相关元素
        this.variableSelection = document.getElementById('variable-selection');
        this.variableList = document.getElementById('variable-list');
        this.regressionVariables = document.getElementById('regression-variables');
        this.xVariables = document.getElementById('x-variables');
        this.yVariables = document.getElementById('y-variables');
        this.regressionValidation = document.getElementById('regression-validation');

        // 分析选项相关元素
        this.analysisOptions = document.getElementById('analysis-options');
        this.runAnalysisBtn = document.getElementById('run-analysis');
        this.clearResultsBtn = document.getElementById('clear-results');

        // 分析结果相关元素
        this.analysisResults = document.getElementById('analysis-results');
        this.resultsContainer = document.getElementById('results-container');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 文件选择按钮点击事件
        this.fileSelectBtn.addEventListener('click', () => {
            this.fileInput.click();
        });

        // 文件输入变化事件
        this.fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.handleFileUpload(e.target.files[0]);
            }
        });

        // 拖拽事件
        this.fileDropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.fileDropZone.classList.add('dragover');
        });

        this.fileDropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.fileDropZone.classList.remove('dragover');
        });

        this.fileDropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            this.fileDropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.handleFileUpload(files[0]);
            }
        });

        // 运行分析按钮事件
        this.runAnalysisBtn.addEventListener('click', () => {
            this.runAnalysis();
        });



        // 清空结果按钮事件
        this.clearResultsBtn.addEventListener('click', () => {
            this.clearResults();
        });

        // 回归分析选项变化事件
        document.addEventListener('change', (e) => {
            if (e.target.id === 'regression') {
                this.toggleRegressionVariables(e.target.checked);
            }
        });
    }

    /**
     * 处理文件上传
     * @param {File} file - 上传的文件
     */
    async handleFileUpload(file) {
        // 验证文件类型
        if (!this.validateFile(file)) {
            return;
        }

        // 显示加载状态
        this.showLoadingState();

        try {
            // 使用数据管理器读取文件
            const result = await window.dataManager.readExcelFile(file);
            
            if (result.success) {
                this.currentData = result;
                this.showFileInfo(result);
                this.showDataOverview(result);
                this.showVariableSelection(result);
                this.showAnalysisOptions();

                // 平滑滚动到数据概览区域
                setTimeout(() => {
                    document.getElementById('data-overview').scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }, 300);
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('文件处理失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 验证文件
     * @param {File} file - 文件对象
     * @returns {boolean} 是否有效
     */
    validateFile(file) {
        // 检查文件类型
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];
        
        const validExtensions = ['.xlsx', '.xls'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        
        if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
            this.showError('请选择有效的Excel文件（.xlsx 或 .xls）');
            return false;
        }

        // 检查文件大小（限制为10MB）
        if (file.size > 10 * 1024 * 1024) {
            this.showError('文件大小不能超过10MB');
            return false;
        }

        return true;
    }

    /**
     * 显示文件信息
     * @param {Object} result - 文件处理结果
     */
    showFileInfo(result) {
        this.fileName.textContent = result.fileName;
        this.fileDetails.textContent = `${result.rowCount} 行数据，${result.columns.length} 列，文件大小: ${this.formatFileSize(result.fileSize)}`;
        this.fileInfo.classList.remove('hidden');
    }

    /**
     * 显示数据概览
     * @param {Object} result - 文件处理结果
     */
    showDataOverview(result) {
        // 创建列信息
        this.createColumnInfo(result);

        // 显示数据预览
        this.showDataPreview(result);

        this.dataOverview.classList.remove('hidden');
    }



    /**
     * 创建列信息显示
     * @param {Object} result - 文件处理结果
     */
    createColumnInfo(result) {
        const container = document.createElement('div');
        container.className = 'info-box bg-gray-50 border border-gray-200 text-gray-800';

        const title = document.createElement('h4');
        title.className = 'font-medium mb-2';
        title.textContent = '列信息概览';

        // 创建表格容器
        const tableContainer = document.createElement('div');
        tableContainer.className = 'column-info-container';

        const table = document.createElement('table');
        table.className = 'column-info-table';

        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        const headers = [
            { key: 'name', label: '列名', width: '120px' },
            { key: 'type', label: '数据类型', width: '80px' },
            { key: 'valid', label: '有效值', width: '60px' },
            { key: 'missing', label: '缺失值', width: '60px' },
            { key: 'unique', label: '唯一值', width: '60px' },
            { key: 'range', label: '数据范围', width: '140px' },
            { key: 'quality', label: '质量评分', width: '80px' },
            { key: 'usage', label: '建议用途', width: '80px' }
        ];

        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header.label;
            th.style.width = header.width;
            th.style.minWidth = header.width;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');

        result.columns.forEach(column => {
            const columnInfo = window.dataManager.getColumnInfo(column);
            const row = document.createElement('tr');

            // 列名
            const nameCell = document.createElement('td');
            nameCell.textContent = column;
            nameCell.title = column; // tooltip显示完整列名
            row.appendChild(nameCell);

            // 数据类型
            const typeCell = document.createElement('td');
            const typeIcon = this.getDataTypeIcon(columnInfo.dataType);
            typeCell.innerHTML = `${typeIcon} ${columnInfo.dataTypeChinese}`;
            typeCell.title = `原始类型: ${columnInfo.dataType}`;
            row.appendChild(typeCell);

            // 有效值
            const validCell = document.createElement('td');
            validCell.textContent = columnInfo.validCount;
            validCell.title = `有效数据: ${columnInfo.validCount} 个`;
            row.appendChild(validCell);

            // 缺失值
            const missingCell = document.createElement('td');
            missingCell.textContent = columnInfo.nullCount;
            missingCell.className = columnInfo.nullCount > 0 ? 'missing-data' : 'complete-data';
            missingCell.title = `缺失数据: ${columnInfo.nullCount} 个`;
            row.appendChild(missingCell);

            // 唯一值
            const uniqueCell = document.createElement('td');
            uniqueCell.textContent = columnInfo.uniqueCount;
            uniqueCell.title = `不同值的个数: ${columnInfo.uniqueCount}`;
            row.appendChild(uniqueCell);

            // 数据范围
            const rangeCell = document.createElement('td');
            rangeCell.textContent = columnInfo.dataRange;
            rangeCell.title = `数据范围: ${columnInfo.dataRange}`;
            row.appendChild(rangeCell);

            // 质量评分
            const qualityCell = document.createElement('td');
            qualityCell.textContent = `${columnInfo.qualityScore}分`;
            qualityCell.className = this.getQualityScoreClass(columnInfo.qualityScore);
            qualityCell.title = `数据质量评分: ${columnInfo.qualityScore}/100`;
            row.appendChild(qualityCell);

            // 建议用途
            const usageCell = document.createElement('td');
            usageCell.textContent = columnInfo.suggestedUsage;
            usageCell.title = `建议用作: ${columnInfo.suggestedUsage}`;
            row.appendChild(usageCell);

            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        tableContainer.appendChild(table);

        container.appendChild(title);
        container.appendChild(tableContainer);

        this.columnInfo.innerHTML = '';
        this.columnInfo.appendChild(container);
    }

    /**
     * 显示数据预览
     * @param {Object} result - 文件处理结果
     */
    showDataPreview(result) {
        const previewData = window.dataManager.getPreviewData(5);
        const table = this.createDataTable(result.columns, previewData);

        this.dataTableContainer.innerHTML = '';
        this.dataTableContainer.appendChild(table);
    }

    /**
     * 创建数据表格
     * @param {Array} columns - 列名数组
     * @param {Array} data - 数据数组
     * @returns {HTMLElement} 表格元素
     */
    createDataTable(columns, data) {
        // 创建容器
        const container = document.createElement('div');
        container.className = 'data-preview-container';

        const table = document.createElement('table');
        table.className = 'data-preview-table';

        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            th.title = column; // 添加tooltip显示完整列名
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');

        data.forEach((row) => {
            const tr = document.createElement('tr');

            columns.forEach(column => {
                const td = document.createElement('td');
                const value = row[column];
                const displayValue = value !== null && value !== undefined ? String(value) : '';
                td.textContent = displayValue;
                td.title = displayValue; // 添加tooltip显示完整内容
                tr.appendChild(td);
            });

            tbody.appendChild(tr);
        });

        table.appendChild(tbody);
        container.appendChild(table);
        return container;
    }

    /**
     * 显示变量选择
     * @param {Object} result - 文件处理结果
     */
    showVariableSelection() {
        const numericColumns = window.dataManager.getNumericColumns();

        if (numericColumns.length === 0) {
            this.variableSelection.classList.add('hidden');
            return;
        }

        // 创建一般分析变量选择
        this.createGeneralVariableSelection(numericColumns);

        // 创建回归分析变量选择
        this.createRegressionVariableSelection(numericColumns);

        this.variableSelection.classList.remove('hidden');
    }

    /**
     * 创建一般分析变量选择
     * @param {Array} numericColumns - 数值列数组
     */
    createGeneralVariableSelection(numericColumns) {
        this.variableList.innerHTML = '';

        const title = document.createElement('h4');
        title.className = 'font-medium text-gray-900 mb-3';
        title.textContent = '选择要分析的变量';

        const selectAllContainer = document.createElement('div');
        selectAllContainer.className = 'mb-3 p-2 bg-gray-50 rounded';
        selectAllContainer.innerHTML = `
            <label class="flex items-center cursor-pointer">
                <input type="checkbox" id="select-all-variables" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                <span class="ml-2 text-sm font-medium text-gray-700">全选/取消全选</span>
            </label>
        `;

        const variablesGrid = document.createElement('div');
        variablesGrid.className = 'variable-grid';

        numericColumns.forEach(column => {
            const variableItem = document.createElement('label');
            variableItem.className = 'variable-item flex items-center hover:bg-gray-50 cursor-pointer';
            variableItem.innerHTML = `
                <input type="checkbox" class="variable-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                       value="${column}" checked>
                <span class="ml-2 text-gray-700">${column}</span>
            `;
            variablesGrid.appendChild(variableItem);
        });

        this.variableList.appendChild(title);
        this.variableList.appendChild(selectAllContainer);
        this.variableList.appendChild(variablesGrid);

        // 绑定全选事件
        document.getElementById('select-all-variables').addEventListener('change', (e) => {
            const checkboxes = document.querySelectorAll('.variable-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
            });
            this.updateSelectedVariables();
        });

        // 绑定变量选择事件
        document.querySelectorAll('.variable-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedVariables();
                this.updateSelectAllState();
            });
        });

        // 初始化选择状态
        this.updateSelectedVariables();
    }

    /**
     * 创建回归分析变量选择
     * @param {Array} numericColumns - 数值列数组
     */
    createRegressionVariableSelection(numericColumns) {
        // 创建自变量选择
        this.xVariables.innerHTML = '';
        numericColumns.forEach(column => {
            const variableInfo = this.getVariableInfo(column);
            const variableItem = document.createElement('label');
            variableItem.className = 'variable-item flex items-center hover:bg-gray-50 cursor-pointer';
            variableItem.dataset.variable = column;
            variableItem.draggable = true;
            variableItem.innerHTML = `
                <input type="checkbox" class="x-variable-checkbox rounded border-gray-300 text-green-600 focus:ring-green-500"
                       value="${column}">
                <div class="ml-2 flex-1">
                    <span class="text-gray-700 variable-name">${column}</span>
                    <div class="text-xs text-gray-500">
                        <span class="variable-type-badge ${this.getTypeColorClass(variableInfo.dataType)}">${variableInfo.dataTypeChinese}</span>
                        <span class="ml-1">${variableInfo.suggestedUsage}</span>
                    </div>
                </div>
                <div class="ml-2 text-gray-400">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                    </svg>
                </div>
            `;
            this.xVariables.appendChild(variableItem);
        });

        // 创建因变量选择
        this.yVariables.innerHTML = '';
        numericColumns.forEach(column => {
            const variableInfo = this.getVariableInfo(column);
            const variableItem = document.createElement('label');
            variableItem.className = 'variable-item flex items-center hover:bg-gray-50 cursor-pointer';
            variableItem.dataset.variable = column;
            variableItem.draggable = true;
            variableItem.innerHTML = `
                <input type="radio" class="y-variable-radio border-gray-300 text-blue-600 focus:ring-blue-500"
                       name="y-variable" value="${column}">
                <div class="ml-2 flex-1">
                    <span class="text-gray-700 variable-name">${column}</span>
                    <div class="text-xs text-gray-500">
                        <span class="variable-type-badge ${this.getTypeColorClass(variableInfo.dataType)}">${variableInfo.dataTypeChinese}</span>
                        <span class="ml-1">${variableInfo.suggestedUsage}</span>
                    </div>
                </div>
                <div class="ml-2 text-gray-400">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                    </svg>
                </div>
            `;
            this.yVariables.appendChild(variableItem);
        });

        // 绑定回归变量选择事件
        document.querySelectorAll('.x-variable-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateRegressionVariables();
                this.updateVariableExclusivity();
            });
        });

        document.querySelectorAll('.y-variable-radio').forEach(radio => {
            radio.addEventListener('change', () => {
                this.updateRegressionVariables();
                this.updateVariableExclusivity();
            });
        });

        // 初始化拖拽功能
        this.initializeDragAndDrop();
    }

    /**
     * 更新选中的变量
     */
    updateSelectedVariables() {
        const selectedCheckboxes = document.querySelectorAll('.variable-checkbox:checked');
        this.selectedVariables.general = Array.from(selectedCheckboxes).map(cb => cb.value);
    }

    /**
     * 更新全选状态
     */
    updateSelectAllState() {
        const allCheckboxes = document.querySelectorAll('.variable-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.variable-checkbox:checked');
        const selectAllCheckbox = document.getElementById('select-all-variables');

        if (checkedCheckboxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 更新回归分析变量
     */
    updateRegressionVariables() {
        // 更新自变量
        const selectedXCheckboxes = document.querySelectorAll('.x-variable-checkbox:checked');
        this.selectedVariables.xVariables = Array.from(selectedXCheckboxes).map(cb => cb.value);

        // 更新因变量
        const selectedYRadio = document.querySelector('.y-variable-radio:checked');
        this.selectedVariables.yVariables = selectedYRadio ? [selectedYRadio.value] : [];

        // 更新实时预览
        this.updateVariablePreview();

        // 验证回归分析变量选择
        this.validateRegressionVariables();
    }

    /**
     * 验证回归分析变量选择
     */
    validateRegressionVariables() {
        const hasXVariable = this.selectedVariables.xVariables.length > 0;
        const hasYVariable = this.selectedVariables.yVariables.length > 0;

        if (document.getElementById('regression').checked) {
            if (!hasXVariable || !hasYVariable) {
                this.regressionValidation.classList.remove('hidden');
                return false;
            } else {
                this.regressionValidation.classList.add('hidden');
                return true;
            }
        }

        this.regressionValidation.classList.add('hidden');
        return true;
    }

    /**
     * 更新变量互斥选择逻辑
     */
    updateVariableExclusivity() {
        // 获取当前选中的因变量
        const selectedYVariable = document.querySelector('.y-variable-radio:checked');
        const selectedYValue = selectedYVariable ? selectedYVariable.value : null;

        // 获取当前选中的自变量
        const selectedXVariables = Array.from(document.querySelectorAll('.x-variable-checkbox:checked'))
            .map(cb => cb.value);

        // 更新自变量选择状态
        document.querySelectorAll('.x-variable-checkbox').forEach(checkbox => {
            const variableItem = checkbox.closest('.variable-item');
            if (selectedYValue && checkbox.value === selectedYValue) {
                // 如果该变量被选为因变量，禁用自变量选择
                checkbox.disabled = true;
                checkbox.checked = false;
                variableItem.classList.add('variable-disabled');
            } else {
                // 否则启用自变量选择
                checkbox.disabled = false;
                variableItem.classList.remove('variable-disabled');
            }
        });

        // 更新因变量选择状态
        document.querySelectorAll('.y-variable-radio').forEach(radio => {
            const variableItem = radio.closest('.variable-item');
            if (selectedXVariables.includes(radio.value)) {
                // 如果该变量被选为自变量，禁用因变量选择
                radio.disabled = true;
                if (radio.checked) {
                    radio.checked = false;
                }
                variableItem.classList.add('variable-disabled');
            } else {
                // 否则启用因变量选择
                radio.disabled = false;
                variableItem.classList.remove('variable-disabled');
            }
        });

        // 更新选中状态的视觉效果
        this.updateVariableSelectionStyles();
    }

    /**
     * 更新变量选择的视觉样式
     */
    updateVariableSelectionStyles() {
        // 更新自变量选择样式
        document.querySelectorAll('.x-variable-checkbox').forEach(checkbox => {
            const variableItem = checkbox.closest('.variable-item');
            if (checkbox.checked) {
                variableItem.classList.add('variable-selected');
            } else {
                variableItem.classList.remove('variable-selected');
            }
        });

        // 更新因变量选择样式
        document.querySelectorAll('.y-variable-radio').forEach(radio => {
            const variableItem = radio.closest('.variable-item');
            if (radio.checked) {
                variableItem.classList.add('variable-selected');
            } else {
                variableItem.classList.remove('variable-selected');
            }
        });
    }

    /**
     * 获取变量信息
     * @param {string} columnName - 列名
     * @returns {Object} 变量信息
     */
    getVariableInfo(columnName) {
        try {
            return window.dataManager.getColumnInfo(columnName);
        } catch (error) {
            // 如果获取失败，返回默认信息
            return {
                dataType: 'numeric',
                dataTypeChinese: '数值型',
                suggestedUsage: '连续变量'
            };
        }
    }

    /**
     * 获取变量类型对应的颜色类
     * @param {string} dataType - 数据类型
     * @returns {string} CSS类名
     */
    getTypeColorClass(dataType) {
        const colorMap = {
            'numeric': 'bg-blue-100 text-blue-800',
            'text': 'bg-green-100 text-green-800',
            'date': 'bg-purple-100 text-purple-800',
            'boolean': 'bg-yellow-100 text-yellow-800',
            'mixed': 'bg-gray-100 text-gray-800',
            'empty': 'bg-red-100 text-red-800'
        };
        return colorMap[dataType] || 'bg-gray-100 text-gray-800';
    }

    /**
     * 更新变量选择预览
     */
    updateVariablePreview() {
        const xCount = this.selectedVariables.xVariables.length;
        const yCount = this.selectedVariables.yVariables.length;

        // 更新计数显示
        const xCountElement = document.getElementById('x-count');
        const yCountElement = document.getElementById('y-count');

        if (xCountElement) {
            xCountElement.textContent = `(${xCount}个已选)`;
        }

        if (yCountElement) {
            yCountElement.textContent = `(${yCount}个已选)`;
        }

        // 更新预览列表
        const xListElement = document.getElementById('x-variables-list');
        const yListElement = document.getElementById('y-variables-list');
        const previewElement = document.getElementById('variable-preview');

        if (xListElement) {
            xListElement.textContent = xCount > 0 ? this.selectedVariables.xVariables.join(', ') : '未选择';
        }

        if (yListElement) {
            yListElement.textContent = yCount > 0 ? this.selectedVariables.yVariables.join(', ') : '未选择';
        }

        // 显示或隐藏预览区域
        if (previewElement) {
            if (xCount > 0 || yCount > 0) {
                previewElement.classList.remove('hidden');
            } else {
                previewElement.classList.add('hidden');
            }
        }
    }

    /**
     * 初始化拖拽功能
     */
    initializeDragAndDrop() {
        // 为所有变量项添加拖拽事件
        document.querySelectorAll('.variable-item[draggable="true"]').forEach(item => {
            item.addEventListener('dragstart', this.handleDragStart.bind(this));
            item.addEventListener('dragend', this.handleDragEnd.bind(this));
        });

        // 为拖拽区域添加事件
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', this.handleDragOver.bind(this));
            zone.addEventListener('dragenter', this.handleDragEnter.bind(this));
            zone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            zone.addEventListener('drop', this.handleDrop.bind(this));
        });
    }

    /**
     * 处理拖拽开始
     * @param {DragEvent} e - 拖拽事件
     */
    handleDragStart(e) {
        const variableName = e.target.dataset.variable;
        e.dataTransfer.setData('text/plain', variableName);
        e.target.classList.add('dragging');
    }

    /**
     * 处理拖拽结束
     * @param {DragEvent} e - 拖拽事件
     */
    handleDragEnd(e) {
        e.target.classList.remove('dragging');
        // 移除所有拖拽状态
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.classList.remove('drag-over');
        });
    }

    /**
     * 处理拖拽悬停
     * @param {DragEvent} e - 拖拽事件
     */
    handleDragOver(e) {
        e.preventDefault();
    }

    /**
     * 处理拖拽进入
     * @param {DragEvent} e - 拖拽事件
     */
    handleDragEnter(e) {
        e.preventDefault();
        e.currentTarget.classList.add('drag-over');
    }

    /**
     * 处理拖拽离开
     * @param {DragEvent} e - 拖拽事件
     */
    handleDragLeave(e) {
        e.currentTarget.classList.remove('drag-over');
    }

    /**
     * 处理拖拽放置
     * @param {DragEvent} e - 拖拽事件
     */
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('drag-over');

        const variableName = e.dataTransfer.getData('text/plain');
        const targetType = e.currentTarget.dataset.variableType;

        if (variableName && targetType) {
            this.moveVariableToTarget(variableName, targetType);
        }
    }

    /**
     * 移动变量到目标区域
     * @param {string} variableName - 变量名
     * @param {string} targetType - 目标类型 ('x' 或 'y')
     */
    moveVariableToTarget(variableName, targetType) {
        if (targetType === 'x') {
            // 移动到自变量
            const checkbox = document.querySelector(`.x-variable-checkbox[value="${variableName}"]`);
            if (checkbox && !checkbox.disabled) {
                checkbox.checked = true;
                // 如果该变量在因变量中被选中，取消选中
                const radio = document.querySelector(`.y-variable-radio[value="${variableName}"]`);
                if (radio) {
                    radio.checked = false;
                }
            }
        } else if (targetType === 'y') {
            // 移动到因变量
            const radio = document.querySelector(`.y-variable-radio[value="${variableName}"]`);
            if (radio && !radio.disabled) {
                radio.checked = true;
                // 如果该变量在自变量中被选中，取消选中
                const checkbox = document.querySelector(`.x-variable-checkbox[value="${variableName}"]`);
                if (checkbox) {
                    checkbox.checked = false;
                }
            }
        }

        // 更新变量状态
        this.updateRegressionVariables();
        this.updateVariableExclusivity();
    }

    /**
     * 切换回归变量选择显示
     * @param {boolean} show - 是否显示
     */
    toggleRegressionVariables(show) {
        if (show) {
            this.regressionVariables.classList.remove('hidden');
            this.validateRegressionVariables();
        } else {
            this.regressionVariables.classList.add('hidden');
            this.regressionValidation.classList.add('hidden');
        }
    }

    /**
     * 显示分析选项
     */
    showAnalysisOptions() {
        this.analysisOptions.classList.remove('hidden');
    }

    /**
     * 运行分析
     */
    runAnalysis() {
        if (!this.currentData) {
            this.showError('请先上传数据文件');
            return;
        }

        // 获取选中的分析选项
        const selectedAnalyses = this.getSelectedAnalyses();

        if (selectedAnalyses.length === 0) {
            this.showError('请至少选择一种分析方法');
            return;
        }

        // 验证变量选择
        if (!this.validateAnalysisSelection(selectedAnalyses)) {
            return;
        }

        // 显示分析结果区域
        this.analysisResults.classList.remove('hidden');
        this.resultsContainer.innerHTML = '<p class="text-gray-600">正在进行分析...</p>';

        // 执行分析
        try {
            this.performAnalyses(selectedAnalyses);

            // 平滑滚动到结果区域
            setTimeout(() => {
                document.getElementById('analysis-results').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 500);
        } catch (error) {
            this.showError('分析过程中出错: ' + error.message);
        }
    }

    /**
     * 验证分析选择
     * @param {Array} selectedAnalyses - 选中的分析类型
     * @returns {boolean} 验证是否通过
     */
    validateAnalysisSelection(selectedAnalyses) {
        // 检查是否选择了变量
        if (this.selectedVariables.general.length === 0) {
            this.showError('请至少选择一个变量进行分析');
            return false;
        }

        // 如果选择了回归分析，验证回归变量
        if (selectedAnalyses.includes('regression')) {
            if (!this.validateRegressionVariables()) {
                this.showError('回归分析需要至少选择一个自变量和一个因变量');
                return false;
            }
        }

        // 检查相关分析是否有足够的变量
        if (selectedAnalyses.includes('correlation') && this.selectedVariables.general.length < 2) {
            this.showError('相关分析需要至少选择两个变量');
            return false;
        }

        return true;
    }

    /**
     * 执行选中的分析
     * @param {Array} selectedAnalyses - 选中的分析类型
     */
    performAnalyses(selectedAnalyses) {
        this.resultsContainer.innerHTML = '';

        // 使用选中的变量
        const selectedColumns = this.selectedVariables.general;

        if (selectedColumns.length === 0) {
            this.resultsContainer.innerHTML = '<p class="text-yellow-600">没有选择变量，无法进行统计分析</p>';
            return;
        }

        selectedAnalyses.forEach(analysisType => {
            switch (analysisType) {
                case 'basic-stats':
                    this.performBasicStats(selectedColumns);
                    break;
                case 'frequency-dist':
                    this.performFrequencyDistribution(selectedColumns);
                    break;
                case 'correlation':
                    this.performCorrelationAnalysis(selectedColumns);
                    break;
                case 't-test':
                    this.performTTest(selectedColumns);
                    break;
                case 'regression':
                    this.performRegressionAnalysis();
                    break;
            }
        });
    }

    /**
     * 执行基本统计分析
     * @param {Array} numericColumns - 数值列数组
     */
    performBasicStats(numericColumns) {
        const container = this.createResultSection('基本描述统计');

        // 收集所有变量的统计数据
        const allStats = [];
        const validColumns = [];

        numericColumns.forEach(column => {
            const data = window.dataManager.getNumericColumn(column);

            try {
                const stats = window.statisticsCalculator.calculateDescriptiveStats(data);
                allStats.push(stats);
                validColumns.push(column);
            } catch (error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-4 mb-3';
                errorDiv.innerHTML = `<p class="text-red-800 text-sm">列 "${column}" 分析失败: ${error.message}</p>`;
                container.appendChild(errorDiv);
            }
        });

        // 创建水平统计表格
        if (allStats.length > 0) {
            const statsTable = this.createHorizontalStatsTable(validColumns, allStats);
            container.appendChild(statsTable);
        }

        // 为每个变量创建箱线图
        validColumns.forEach((column, index) => {
            const stats = allStats[index];

            // 创建箱线图
            const chartContainer = window.chartRenderer.createChartContainer(
                `boxplot-${column}`,
                `${column} - 箱线图`
            );
            const canvas = window.chartRenderer.createCanvas(`boxplot-canvas-${column}`);
            chartContainer.canvasContainer.appendChild(canvas);
            container.appendChild(chartContainer.container);

            window.chartRenderer.renderBoxPlot(`boxplot-canvas-${column}`, stats, `${column} - 数据分布`);
        });
    }

    /**
     * 执行频率分布分析
     * @param {Array} numericColumns - 数值列数组
     */
    performFrequencyDistribution(numericColumns) {
        const container = this.createResultSection('频率分布分析');

        numericColumns.forEach(column => {
            const data = window.dataManager.getNumericColumn(column);

            try {
                const freqDist = window.statisticsCalculator.calculateFrequencyDistribution(data);

                // 创建直方图
                const chartContainer = window.chartRenderer.createChartContainer(
                    `histogram-${column}`,
                    `${column} - 频率分布`
                );
                const canvas = window.chartRenderer.createCanvas(`histogram-canvas-${column}`);
                chartContainer.canvasContainer.appendChild(canvas);
                container.appendChild(chartContainer.container);

                window.chartRenderer.renderHistogram(`histogram-canvas-${column}`, freqDist, `${column} - 频率分布`);

            } catch (error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-4 mb-4';
                errorDiv.innerHTML = `<p class="text-red-800">列 "${column}" 频率分布分析失败: ${error.message}</p>`;
                container.appendChild(errorDiv);
            }
        });
    }

    /**
     * 执行相关性可视化分析
     * @param {Array} numericColumns - 数值列数组
     */
    performCorrelationAnalysis(numericColumns) {
        if (numericColumns.length < 2) {
            const container = this.createResultSection('相关性可视化');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-yellow-50 border border-yellow-200 rounded-md p-4';
            errorDiv.innerHTML = '<p class="text-yellow-800">需要至少两个数值列才能进行相关性分析</p>';
            container.appendChild(errorDiv);
            return;
        }

        const container = this.createResultSection('相关性可视化');

        // 创建相关性可视化
        this.createCorrelationVisualization(container, numericColumns);
    }

    /**
     * 创建相关性可视化
     * @param {HTMLElement} container - 容器元素
     * @param {Array} numericColumns - 数值列数组
     */
    createCorrelationVisualization(container, numericColumns) {
        const vizContainer = document.createElement('div');
        vizContainer.className = 'correlation-visualization-container';

        // 计算所有相关系数
        const correlations = [];
        const correlationMatrix = {};

        for (let i = 0; i < numericColumns.length; i++) {
            correlationMatrix[numericColumns[i]] = {};
            for (let j = 0; j < numericColumns.length; j++) {
                try {
                    const data1 = window.dataManager.getNumericColumn(numericColumns[i]);
                    const data2 = window.dataManager.getNumericColumn(numericColumns[j]);
                    const correlation = window.statisticsCalculator.calculateCorrelation(data1, data2);

                    correlationMatrix[numericColumns[i]][numericColumns[j]] = correlation.correlation;

                    if (i < j) { // 避免重复
                        correlations.push({
                            pair: `${numericColumns[i]} - ${numericColumns[j]}`,
                            value: correlation.correlation,
                            x: numericColumns[i],
                            y: numericColumns[j]
                        });
                    }
                } catch (error) {
                    correlationMatrix[numericColumns[i]][numericColumns[j]] = i === j ? 1 : 0;
                }
            }
        }

        // 创建相关性摘要
        this.createCorrelationSummary(vizContainer, correlations);

        // 创建标签页导航
        this.createCorrelationTabs(vizContainer, correlations, correlationMatrix, numericColumns);

        container.appendChild(vizContainer);
    }

    /**
     * 创建相关性分析标签页
     * @param {HTMLElement} container - 容器元素
     * @param {Array} correlations - 相关系数数组
     * @param {Object} correlationMatrix - 相关系数矩阵
     * @param {Array} numericColumns - 数值列数组
     */
    createCorrelationTabs(container, correlations, correlationMatrix, numericColumns) {
        // 创建标签页导航
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'correlation-tabs';

        const tabs = [
            { id: 'bar-chart', label: '📊 相关强度', active: true },
            { id: 'heatmap', label: '🔥 热力图' },
            { id: 'scatter', label: '📈 散点图' }
        ];

        tabs.forEach(tab => {
            const tabElement = document.createElement('div');
            tabElement.className = `correlation-tab ${tab.active ? 'active' : ''}`;
            tabElement.textContent = tab.label;
            tabElement.onclick = () => this.switchCorrelationTab(tab.id);
            tabsContainer.appendChild(tabElement);
        });

        container.appendChild(tabsContainer);

        // 创建标签页内容
        this.createTabContent(container, 'bar-chart', correlations, null, true);
        this.createTabContent(container, 'heatmap', correlationMatrix, numericColumns, false);
        this.createTabContent(container, 'scatter', correlations, null, false);
    }

    /**
     * 切换相关性分析标签页
     * @param {string} activeTabId - 激活的标签页ID
     */
    switchCorrelationTab(activeTabId) {
        // 更新标签页状态
        document.querySelectorAll('.correlation-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.correlation-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // 激活选中的标签页
        const activeTab = document.querySelector(`.correlation-tab:nth-child(${this.getTabIndex(activeTabId)})`);
        const activeContent = document.getElementById(`correlation-${activeTabId}`);

        if (activeTab) activeTab.classList.add('active');
        if (activeContent) activeContent.classList.add('active');
    }

    /**
     * 获取标签页索引
     * @param {string} tabId - 标签页ID
     * @returns {number} 标签页索引
     */
    getTabIndex(tabId) {
        const tabMap = { 'bar-chart': 1, 'heatmap': 2, 'scatter': 3 };
        return tabMap[tabId] || 1;
    }

    /**
     * 创建标签页内容
     * @param {HTMLElement} container - 容器元素
     * @param {string} tabId - 标签页ID
     * @param {*} data - 数据
     * @param {*} extraData - 额外数据（用于热力图的numericColumns）
     * @param {boolean} isActive - 是否激活
     */
    createTabContent(container, tabId, data, extraData = null, isActive = false) {
        const contentDiv = document.createElement('div');
        contentDiv.id = `correlation-${tabId}`;
        contentDiv.className = `correlation-tab-content ${isActive ? 'active' : ''}`;

        switch (tabId) {
            case 'bar-chart':
                this.createCorrelationBarChart(contentDiv, data);
                break;
            case 'heatmap':
                this.createCorrelationHeatmap(contentDiv, data, extraData); // data=correlationMatrix, extraData=numericColumns
                break;
            case 'scatter':
                this.createEnhancedScatterPlots(contentDiv, data);
                break;
        }

        container.appendChild(contentDiv);
    }

    /**
     * 创建相关系数热力图
     * @param {HTMLElement} container - 容器元素
     * @param {Object} correlationMatrix - 相关系数矩阵
     * @param {Array} numericColumns - 数值列数组
     */
    createCorrelationHeatmap(container, correlationMatrix, numericColumns) {
        const chartContainer = document.createElement('div');
        chartContainer.className = 'correlation-heatmap-container';

        const title = document.createElement('h4');
        title.className = 'text-lg font-semibold text-gray-900 mb-3';
        title.textContent = '相关系数热力图';

        // 创建图例
        const legend = this.createHeatmapLegend();

        chartContainer.appendChild(title);
        chartContainer.appendChild(legend);

        // 创建真正的热力图
        const heatmapElement = this.createMatrixHeatmap(correlationMatrix, numericColumns);
        chartContainer.appendChild(heatmapElement);

        container.appendChild(chartContainer);
    }

    /**
     * 计算最优热力图布局配置
     * @param {number} variableCount - 变量数量
     * @returns {Object} 布局配置对象
     */
    calculateOptimalHeatmapLayout(variableCount) {
        // 获取可用空间
        const availableWidth = Math.min(900, window.innerWidth - 120);
        const availableHeight = Math.min(700, window.innerHeight - 250);

        // 预留空间配置
        const yLabelWidth = 120;
        const xLabelHeight = 100;
        const padding = 20;

        // 计算矩阵可用空间
        const matrixAvailableWidth = availableWidth - yLabelWidth - padding * 2;
        const matrixAvailableHeight = availableHeight - xLabelHeight - padding * 2;

        // 根据变量数量确定单元格大小范围
        let maxCellSize, minCellSize;
        if (variableCount <= 5) {
            maxCellSize = 90;
            minCellSize = 60;
        } else if (variableCount <= 8) {
            maxCellSize = 70;
            minCellSize = 45;
        } else if (variableCount <= 12) {
            maxCellSize = 55;
            minCellSize = 35;
        } else {
            maxCellSize = 45;
            minCellSize = 25;
        }

        // 计算最优单元格大小
        const cellSizeByWidth = Math.max(minCellSize, Math.min(maxCellSize, matrixAvailableWidth / variableCount));
        const cellSizeByHeight = Math.max(minCellSize, Math.min(maxCellSize, matrixAvailableHeight / variableCount));
        const cellSize = Math.min(cellSizeByWidth, cellSizeByHeight);

        return {
            cellSize,
            yLabelWidth,
            xLabelHeight,
            padding,
            matrixSize: cellSize * variableCount,
            containerWidth: cellSize * variableCount + yLabelWidth + padding * 2,
            containerHeight: cellSize * variableCount + xLabelHeight + padding * 2
        };
    }

    /**
     * 创建矩阵热力图
     * @param {Object} correlationMatrix - 相关系数矩阵
     * @param {Array} numericColumns - 数值列数组
     * @returns {HTMLElement} 热力图元素
     */
    createMatrixHeatmap(correlationMatrix, numericColumns) {
        const heatmapWrapper = document.createElement('div');
        heatmapWrapper.className = 'matrix-heatmap-wrapper';

        // 计算最优容器尺寸和单元格大小
        const layoutConfig = this.calculateOptimalHeatmapLayout(numericColumns.length);
        const cellSize = layoutConfig.cellSize;
        const matrixSize = cellSize * numericColumns.length;

        // 使用布局配置中的尺寸
        const { yLabelWidth, padding, containerWidth, containerHeight } = layoutConfig;

        // 创建主容器（动态尺寸适配）
        const heatmapContainer = document.createElement('div');
        heatmapContainer.className = 'matrix-heatmap-container';
        heatmapContainer.style.cssText = `
            position: relative;
            width: ${containerWidth}px;
            height: ${containerHeight}px;
            margin: 20px auto;
            font-family: 'Arial', sans-serif;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: visible;
        `;

        // 创建Y轴标签（使用动态定位）
        const yLabelsContainer = document.createElement('div');
        yLabelsContainer.className = 'y-labels-container';
        yLabelsContainer.style.cssText = `
            position: absolute;
            left: ${padding}px;
            top: ${padding}px;
            width: ${yLabelWidth}px;
            height: ${matrixSize}px;
        `;

        numericColumns.forEach((label, index) => {
            const labelElement = document.createElement('div');
            labelElement.className = 'y-axis-label';
            const maxLabelLength = Math.max(8, Math.floor(100 / Math.max(8, cellSize * 0.12)));
            labelElement.textContent = this.truncateLabel(label, maxLabelLength);
            labelElement.title = label;
            labelElement.style.cssText = `
                position: absolute;
                right: 10px;
                top: ${index * cellSize + cellSize/2 - 8}px;
                height: 16px;
                line-height: 16px;
                font-size: ${Math.max(10, Math.min(12, cellSize * 0.15))}px;
                font-weight: 500;
                color: #374151;
                text-align: right;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 90px;
            `;
            yLabelsContainer.appendChild(labelElement);
        });

        // 创建X轴标签（移动到底部，使用动态定位）
        const xLabelsContainer = document.createElement('div');
        xLabelsContainer.className = 'x-labels-container';
        xLabelsContainer.style.cssText = `
            position: absolute;
            left: ${padding + yLabelWidth}px;
            top: ${padding + matrixSize + 40}px;
            width: ${matrixSize}px;
            height: 80px;
        `;

        numericColumns.forEach((label, index) => {
            const labelElement = document.createElement('div');
            labelElement.className = 'x-axis-label';
            const maxLabelLength = Math.max(6, Math.floor(cellSize / 8));
            labelElement.textContent = this.truncateLabel(label, maxLabelLength);
            labelElement.title = label;
            labelElement.style.cssText = `
                position: absolute;
                left: ${index * cellSize + cellSize/2}px;
                top: 20px;
                width: ${cellSize}px;
                height: 60px;
                font-size: ${Math.max(9, Math.min(11, cellSize * 0.13))}px;
                font-weight: 500;
                color: #374151;
                text-align: center;
                transform: translateX(-50%) rotate(-45deg);
                transform-origin: center top;
                white-space: nowrap;
                overflow: visible;
                line-height: 12px;
                display: flex;
                align-items: flex-start;
                justify-content: center;
                z-index: 10;
            `;
            xLabelsContainer.appendChild(labelElement);
        });

        // 创建矩阵网格（使用动态定位）
        const matrixGrid = document.createElement('div');
        matrixGrid.className = 'matrix-grid';
        matrixGrid.style.cssText = `
            position: absolute;
            left: ${padding + yLabelWidth}px;
            top: ${padding}px;
            width: ${matrixSize}px;
            height: ${matrixSize}px;
            border: 2px solid #374151;
        `;

        // 创建矩阵单元格
        for (let i = 0; i < numericColumns.length; i++) {
            for (let j = 0; j < numericColumns.length; j++) {
                const cell = document.createElement('div');
                const correlation = correlationMatrix[numericColumns[i]][numericColumns[j]];

                cell.className = 'matrix-cell';
                cell.style.cssText = `
                    position: absolute;
                    left: ${j * cellSize}px;
                    top: ${i * cellSize}px;
                    width: ${cellSize}px;
                    height: ${cellSize}px;
                    background-color: ${this.getHeatmapColor(correlation)};
                    border: 1px solid #ffffff;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: ${Math.max(10, cellSize * 0.15)}px;
                    font-weight: 600;
                    color: ${this.getTextColor(correlation)};
                    cursor: pointer;
                    transition: all 0.2s ease;
                    box-sizing: border-box;
                `;

                cell.textContent = correlation.toFixed(2);
                cell.title = `${numericColumns[i]} vs ${numericColumns[j]}: ${correlation.toFixed(3)}\n${this.getCorrelationInterpretation(correlation)}`;

                // 添加悬停效果
                cell.addEventListener('mouseenter', () => {
                    cell.style.transform = 'scale(1.1)';
                    cell.style.zIndex = '10';
                    cell.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                });

                cell.addEventListener('mouseleave', () => {
                    cell.style.transform = 'scale(1)';
                    cell.style.zIndex = '1';
                    cell.style.boxShadow = 'none';
                });

                // 添加点击事件
                cell.addEventListener('click', () => {
                    this.showCorrelationDetails(numericColumns[i], numericColumns[j], correlation);
                });

                matrixGrid.appendChild(cell);
            }
        }

        heatmapContainer.appendChild(yLabelsContainer);
        heatmapContainer.appendChild(xLabelsContainer);
        heatmapContainer.appendChild(matrixGrid);
        heatmapWrapper.appendChild(heatmapContainer);

        return heatmapWrapper;
    }

    /**
     * 截断标签文本
     * @param {string} label - 原始标签
     * @param {number} maxLength - 最大长度
     * @returns {string} 截断后的标签
     */
    truncateLabel(label, maxLength) {
        if (label.length <= maxLength) {
            return label;
        }
        return label.substring(0, maxLength - 3) + '...';
    }

    /**
     * 获取文本颜色（基于背景色）
     * @param {number} correlation - 相关系数
     * @returns {string} 文本颜色
     */
    getTextColor(correlation) {
        const abs = Math.abs(correlation);

        // 对于深色背景使用白色文字，浅色背景使用深色文字
        if (abs >= 0.6) {
            return '#ffffff';  // 白色文字用于深色背景
        } else if (abs >= 0.3) {
            return '#1f2937';  // 深色文字用于中等背景
        } else {
            return '#374151';  // 深色文字用于浅色背景
        }
    }

    /**
     * 显示相关系数详细信息
     * @param {string} var1 - 变量1
     * @param {string} var2 - 变量2
     * @param {number} correlation - 相关系数
     */
    showCorrelationDetails(var1, var2, correlation) {
        const interpretation = this.getCorrelationInterpretation(correlation);
        const message = `变量关系详情：\n\n` +
                       `${var1} vs ${var2}\n` +
                       `相关系数: ${correlation.toFixed(3)}\n` +
                       `关系强度: ${interpretation}\n\n` +
                       `${Math.abs(correlation) >= 0.5 ?
                         '这两个变量之间存在较强的关系，值得进一步分析。' :
                         '这两个变量之间的关系较弱。'}`;

        alert(message);
    }

    /**
     * 获取热力图颜色
     * @param {number} correlation - 相关系数值
     * @returns {string} 颜色值
     */
    getHeatmapColor(correlation) {
        const abs = Math.abs(correlation);

        if (correlation > 0) {
            // 正相关 - 红色系
            if (abs >= 0.8) return 'rgba(220, 38, 38, 0.9)';      // 很强正相关
            if (abs >= 0.6) return 'rgba(239, 68, 68, 0.8)';      // 强正相关
            if (abs >= 0.4) return 'rgba(248, 113, 113, 0.7)';    // 中等正相关
            if (abs >= 0.2) return 'rgba(252, 165, 165, 0.6)';    // 弱正相关
            return 'rgba(254, 202, 202, 0.5)';                    // 很弱正相关
        } else if (correlation < 0) {
            // 负相关 - 蓝色系
            if (abs >= 0.8) return 'rgba(29, 78, 216, 0.9)';      // 很强负相关
            if (abs >= 0.6) return 'rgba(37, 99, 235, 0.8)';      // 强负相关
            if (abs >= 0.4) return 'rgba(59, 130, 246, 0.7)';     // 中等负相关
            if (abs >= 0.2) return 'rgba(96, 165, 250, 0.6)';     // 弱负相关
            return 'rgba(147, 197, 253, 0.5)';                    // 很弱负相关
        } else {
            // 无相关 - 灰色
            return 'rgba(156, 163, 175, 0.5)';
        }
    }

    /**
     * 创建热力图图例
     * @returns {HTMLElement} 图例元素
     */
    createHeatmapLegend() {
        const legend = document.createElement('div');
        legend.className = 'heatmap-legend';

        const legendItems = [
            { color: 'rgba(220, 38, 38, 0.9)', label: '强正相关 (0.6-1.0)' },
            { color: 'rgba(248, 113, 113, 0.7)', label: '中等正相关 (0.3-0.6)' },
            { color: 'rgba(156, 163, 175, 0.5)', label: '无相关 (-0.3-0.3)' },
            { color: 'rgba(59, 130, 246, 0.7)', label: '中等负相关 (-0.6--0.3)' },
            { color: 'rgba(29, 78, 216, 0.9)', label: '强负相关 (-1.0--0.6)' }
        ];

        legendItems.forEach(item => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';

            const colorBox = document.createElement('div');
            colorBox.className = 'legend-color';
            colorBox.style.backgroundColor = item.color;

            const label = document.createElement('span');
            label.className = 'legend-label';
            label.textContent = item.label;

            legendItem.appendChild(colorBox);
            legendItem.appendChild(label);
            legend.appendChild(legendItem);
        });

        return legend;
    }

    /**
     * 创建增强散点图
     * @param {HTMLElement} container - 容器元素
     * @param {Array} correlations - 相关系数数组
     */
    createEnhancedScatterPlots(container, correlations) {
        const title = document.createElement('h4');
        title.className = 'text-lg font-semibold text-gray-900 mb-3';
        title.textContent = '增强散点图矩阵（按相关强度排序）';
        container.appendChild(title);

        // 按相关系数绝对值从大到小排序
        const sortedCorrelations = [...correlations].sort((a, b) => Math.abs(b.value) - Math.abs(a.value));

        // 为每对变量创建增强散点图
        sortedCorrelations.forEach((corr, index) => {
            try {
                const data1 = window.dataManager.getNumericColumn(corr.x);
                const data2 = window.dataManager.getNumericColumn(corr.y);

                // 创建散点图容器（增加底部间距）
                const scatterContainer = document.createElement('div');
                scatterContainer.className = 'correlation-chart-container mb-6';

                const scatterTitle = document.createElement('h5');
                scatterTitle.className = 'text-md font-medium text-gray-800 mb-2';
                const interpretation = this.getCorrelationInterpretation(corr.value);
                const rankInfo = index === 0 ? ' 🏆 最强相关' : index === 1 ? ' 🥈 次强相关' : index === 2 ? ' 🥉 第三强' : '';
                scatterTitle.textContent = `${corr.x} vs ${corr.y} (r = ${corr.value.toFixed(3)}, ${interpretation})${rankInfo}`;

                const canvas = document.createElement('canvas');
                canvas.id = `enhanced-scatter-${corr.x}-${corr.y}-${Date.now()}`;

                scatterContainer.appendChild(scatterTitle);
                scatterContainer.appendChild(canvas);
                container.appendChild(scatterContainer);

                // 准备散点图数据
                const scatterData = [];
                for (let i = 0; i < data1.length; i++) {
                    if (!isNaN(data1[i]) && !isNaN(data2[i])) {
                        scatterData.push({ x: data1[i], y: data2[i] });
                    }
                }

                // 计算趋势线
                const trendLine = this.calculateTrendLine(scatterData);

                // 创建Chart.js增强散点图
                new Chart(canvas, {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: '数据点',
                            data: scatterData,
                            backgroundColor: this.getScatterPointColor(corr.value),
                            borderColor: this.getScatterBorderColor(corr.value),
                            borderWidth: 1,
                            pointRadius: this.getScatterPointSize(corr.value),
                            pointHoverRadius: this.getScatterPointSize(corr.value) + 2
                        }, {
                            label: '趋势线',
                            data: trendLine,
                            type: 'line',
                            borderColor: 'rgba(239, 68, 68, 0.8)',
                            backgroundColor: 'transparent',
                            borderWidth: 2,
                            pointRadius: 0,
                            pointHoverRadius: 0,
                            tension: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: true,
                                position: 'top'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        if (context.datasetIndex === 0) {
                                            return `${corr.x}: ${context.parsed.x.toFixed(2)}, ${corr.y}: ${context.parsed.y.toFixed(2)}`;
                                        }
                                        return null;
                                    }
                                },
                                filter: function(tooltipItem) {
                                    return tooltipItem.datasetIndex === 0;
                                }
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: corr.x
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: corr.y
                                }
                            }
                        },
                        onClick: (_, elements) => {
                            if (elements.length > 0) {
                                const element = elements[0];
                                const dataPoint = scatterData[element.index];
                                alert(`数据点: ${corr.x} = ${dataPoint.x.toFixed(2)}, ${corr.y} = ${dataPoint.y.toFixed(2)}`);
                            }
                        }
                    }
                });

            } catch (error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-4 mb-4';
                errorDiv.innerHTML = `<p class="text-red-800">${corr.x} vs ${corr.y} 散点图创建失败: ${error.message}</p>`;
                container.appendChild(errorDiv);
            }
        });
    }

    /**
     * 获取相关系数的解释
     * @param {number} correlation - 相关系数值
     * @returns {string} 解释文本
     */
    getCorrelationInterpretation(correlation) {
        const abs = Math.abs(correlation);
        const direction = correlation > 0 ? '正相关' : correlation < 0 ? '负相关' : '无相关';

        if (abs >= 0.8) {
            return `很强的${direction}`;
        } else if (abs >= 0.6) {
            return `强${direction}`;
        } else if (abs >= 0.4) {
            return `中等${direction}`;
        } else if (abs >= 0.2) {
            return `弱${direction}`;
        } else {
            return '几乎无相关';
        }
    }

    /**
     * 计算趋势线数据
     * @param {Array} data - 散点数据
     * @returns {Array} 趋势线数据
     */
    calculateTrendLine(data) {
        if (data.length < 2) return [];

        // 计算线性回归
        const n = data.length;
        const sumX = data.reduce((sum, point) => sum + point.x, 0);
        const sumY = data.reduce((sum, point) => sum + point.y, 0);
        const sumXY = data.reduce((sum, point) => sum + point.x * point.y, 0);
        const sumXX = data.reduce((sum, point) => sum + point.x * point.x, 0);

        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;

        // 生成趋势线点
        const xMin = Math.min(...data.map(p => p.x));
        const xMax = Math.max(...data.map(p => p.x));

        return [
            { x: xMin, y: slope * xMin + intercept },
            { x: xMax, y: slope * xMax + intercept }
        ];
    }

    /**
     * 获取散点图点颜色
     * @param {number} correlation - 相关系数
     * @returns {string} 颜色值
     */
    getScatterPointColor(correlation) {
        const abs = Math.abs(correlation);

        if (correlation > 0) {
            if (abs >= 0.8) return 'rgba(220, 38, 38, 0.7)';
            if (abs >= 0.6) return 'rgba(239, 68, 68, 0.6)';
            if (abs >= 0.4) return 'rgba(248, 113, 113, 0.5)';
            if (abs >= 0.2) return 'rgba(252, 165, 165, 0.4)';
            return 'rgba(156, 163, 175, 0.4)';
        } else if (correlation < 0) {
            if (abs >= 0.8) return 'rgba(29, 78, 216, 0.7)';
            if (abs >= 0.6) return 'rgba(37, 99, 235, 0.6)';
            if (abs >= 0.4) return 'rgba(59, 130, 246, 0.5)';
            if (abs >= 0.2) return 'rgba(96, 165, 250, 0.4)';
            return 'rgba(156, 163, 175, 0.4)';
        } else {
            return 'rgba(156, 163, 175, 0.4)';
        }
    }

    /**
     * 获取散点图边框颜色
     * @param {number} correlation - 相关系数
     * @returns {string} 颜色值
     */
    getScatterBorderColor(correlation) {
        const abs = Math.abs(correlation);

        if (correlation > 0) {
            if (abs >= 0.6) return 'rgba(220, 38, 38, 1)';
            if (abs >= 0.4) return 'rgba(239, 68, 68, 1)';
            return 'rgba(156, 163, 175, 1)';
        } else if (correlation < 0) {
            if (abs >= 0.6) return 'rgba(29, 78, 216, 1)';
            if (abs >= 0.4) return 'rgba(37, 99, 235, 1)';
            return 'rgba(156, 163, 175, 1)';
        } else {
            return 'rgba(156, 163, 175, 1)';
        }
    }

    /**
     * 获取散点图点大小
     * @param {number} correlation - 相关系数
     * @returns {number} 点大小
     */
    getScatterPointSize(correlation) {
        const abs = Math.abs(correlation);

        if (abs >= 0.8) return 5;
        if (abs >= 0.6) return 4;
        if (abs >= 0.4) return 3;
        if (abs >= 0.2) return 2;
        return 2;
    }

    /**
     * 创建相关性摘要
     * @param {HTMLElement} container - 容器元素
     * @param {Array} correlations - 相关系数数组
     */
    createCorrelationSummary(container, correlations) {
        const summaryContainer = document.createElement('div');
        summaryContainer.className = 'correlation-summary';

        // 计算统计信息
        const values = correlations.map(c => Math.abs(c.value));
        const avgCorr = values.reduce((a, b) => a + b, 0) / values.length;

        // 找到最强相关性
        const strongestCorr = correlations.reduce((max, curr) =>
            Math.abs(curr.value) > Math.abs(max.value) ? curr : max
        );

        // 创建摘要项
        const summaryItems = [
            { label: '变量对数', value: correlations.length },
            { label: '最强相关性', value: strongestCorr.value.toFixed(3) },
            { label: '最强相关对', value: strongestCorr.pair },
            { label: '平均相关强度', value: avgCorr.toFixed(3) }
        ];

        summaryItems.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.className = 'correlation-summary-item';
            itemDiv.innerHTML = `
                <div class="correlation-summary-value">${item.value}</div>
                <div class="correlation-summary-label">${item.label}</div>
            `;
            summaryContainer.appendChild(itemDiv);
        });

        container.appendChild(summaryContainer);
    }

    /**
     * 创建相关系数条形图
     * @param {HTMLElement} container - 容器元素
     * @param {Array} correlations - 相关系数数组
     */
    createCorrelationBarChart(container, correlations) {
        const chartContainer = document.createElement('div');
        chartContainer.className = 'correlation-chart-container';

        const title = document.createElement('h4');
        title.className = 'text-lg font-semibold text-gray-900 mb-3';
        title.textContent = '变量间相关系数';

        const canvas = document.createElement('canvas');
        canvas.id = `correlation-bar-chart-${Date.now()}`;

        chartContainer.appendChild(title);
        chartContainer.appendChild(canvas);
        container.appendChild(chartContainer);

        // 排序相关系数（按绝对值）
        const sortedCorrelations = [...correlations].sort((a, b) => Math.abs(b.value) - Math.abs(a.value));

        // 创建Chart.js条形图
        new Chart(canvas, {
            type: 'bar',
            data: {
                labels: sortedCorrelations.map(c => c.pair),
                datasets: [{
                    label: '相关系数',
                    data: sortedCorrelations.map(c => c.value),
                    backgroundColor: sortedCorrelations.map(c => {
                        const abs = Math.abs(c.value);
                        if (abs >= 0.8) return c.value > 0 ? '#dc2626' : '#2563eb';
                        if (abs >= 0.6) return c.value > 0 ? '#ef4444' : '#3b82f6';
                        if (abs >= 0.4) return c.value > 0 ? '#f87171' : '#60a5fa';
                        if (abs >= 0.2) return c.value > 0 ? '#fca5a5' : '#93c5fd';
                        return '#e5e7eb';
                    }),
                    borderColor: '#374151',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed.y;
                                const interpretation = Math.abs(value) >= 0.8 ? '很强' :
                                                     Math.abs(value) >= 0.6 ? '强' :
                                                     Math.abs(value) >= 0.4 ? '中等' :
                                                     Math.abs(value) >= 0.2 ? '弱' : '很弱';
                                return `相关系数: ${value.toFixed(3)} (${interpretation}${value > 0 ? '正相关' : '负相关'})`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        min: -1,
                        max: 1,
                        title: {
                            display: true,
                            text: '相关系数'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '变量对'
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }



    /**
     * 执行t检验
     * @param {Array} numericColumns - 数值列数组
     */
    performTTest(numericColumns) {
        const container = this.createResultSection('单样本t检验 (One-Sample t-Test)');

        // 初始化t检验解释器
        const tTestExplainer = new window.TTestExplainer();

        numericColumns.forEach(column => {
            const data = window.dataManager.getNumericColumn(column);

            try {
                // 使用样本均值作为假设总体均值（这里可以让用户输入）
                const sampleMean = window.statisticsCalculator.calculateDescriptiveStats(data).mean;
                const hypothesizedMean = Math.round(sampleMean); // 简化：使用四舍五入的均值作为假设值

                const tTestResult = window.statisticsCalculator.performOneSampleTTest(data, hypothesizedMean);

                // 生成业务解释
                const businessExplanation = tTestExplainer.generateBusinessExplanation(tTestResult, column);

                const resultDiv = document.createElement('div');
                resultDiv.className = 'bg-white border rounded-lg p-4 mb-4';
                resultDiv.innerHTML = `
                    <h4 class="font-semibold text-gray-900 mb-3">${column} - t检验结果</h4>

                    <!-- 统计结果 -->
                    <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                        <div>
                            <p><strong>${tTestExplainer.getTerm('sampleMean')}:</strong> ${tTestResult.sampleMean.toFixed(4)}</p>
                            <p><strong>${tTestExplainer.getTerm('hypothesizedMean')}:</strong> ${tTestResult.populationMean}</p>
                            <p><strong>${tTestExplainer.getTerm('sampleStd')}:</strong> ${tTestResult.sampleStd.toFixed(4)}</p>
                            <p><strong>${tTestExplainer.getTerm('standardError')}:</strong> ${tTestResult.standardError.toFixed(4)}</p>
                        </div>
                        <div>
                            <p><strong>${tTestExplainer.getTerm('tStatistic')}:</strong> ${tTestResult.tStatistic.toFixed(4)}</p>
                            <p><strong>${tTestExplainer.getTerm('pValue')}:</strong> ${tTestResult.pValue < 0.001 ? '<0.001' : tTestResult.pValue.toFixed(3)}</p>
                            <p><strong>${tTestExplainer.getTerm('degreesOfFreedom')}:</strong> ${tTestResult.degreesOfFreedom}</p>
                            <p><strong>${tTestExplainer.getTerm('sampleSize')}:</strong> ${tTestResult.n}</p>
                            <p><strong>${tTestExplainer.getTerm('significanceLevel')}:</strong> ${tTestResult.alpha}</p>
                        </div>
                    </div>

                    <!-- 置信区间 -->
                    <div class="mb-4 p-3 bg-gray-50 rounded">
                        <p class="text-sm"><strong>${tTestExplainer.getTerm('confidenceInterval')} (${((1-tTestResult.alpha)*100).toFixed(0)}%):</strong>
                        [${tTestResult.confidenceInterval[0].toFixed(3)}, ${tTestResult.confidenceInterval[1].toFixed(3)}]</p>
                    </div>

                    <!-- 统计结论 -->
                    <div class="mb-4 p-3 bg-blue-50 rounded">
                        <p class="text-blue-800 text-sm"><strong>统计结论 (Statistical Conclusion):</strong> ${tTestResult.interpretation}</p>
                    </div>

                    <!-- 业务解释 -->
                    ${tTestExplainer.generateBusinessHTML(businessExplanation)}
                `;
                container.appendChild(resultDiv);

            } catch (error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-4 mb-4';
                errorDiv.innerHTML = `<p class="text-red-800">列 "${column}" t检验失败: ${error.message}</p>`;
                container.appendChild(errorDiv);
            }
        });
    }

    /**
     * 执行多元线性回归分析
     */
    performRegressionAnalysis() {
        const xVariables = this.selectedVariables.xVariables;
        const yVariables = this.selectedVariables.yVariables;

        if (xVariables.length === 0 || yVariables.length === 0) {
            const container = this.createResultSection('多元线性回归分析');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'bg-yellow-50 border border-yellow-200 rounded-md p-4';
            errorDiv.innerHTML = '<p class="text-yellow-800">请选择自变量和因变量进行回归分析</p>';
            container.appendChild(errorDiv);
            return;
        }

        const container = this.createResultSection('多元线性回归分析');

        // 对每个因变量进行多元回归分析
        yVariables.forEach(yCol => {
            try {
                // 获取因变量数据
                const yData = window.dataManager.getNumericColumn(yCol);

                // 获取所有自变量数据
                const xDataMatrix = [];
                const validXVariables = [];

                xVariables.forEach(xCol => {
                    try {
                        const xData = window.dataManager.getNumericColumn(xCol);
                        xDataMatrix.push(xData);
                        validXVariables.push(xCol);
                    } catch (error) {
                        // 跳过无效的自变量
                    }
                });

                if (validXVariables.length === 0) {
                    throw new Error('没有有效的自变量数据');
                }

                // 执行多元回归分析
                const regression = window.statisticsCalculator.performEnhancedLinearRegression(
                    xDataMatrix,
                    yData,
                    validXVariables,
                    yCol
                );

                // 创建回归分析结果展示
                this.createMultipleRegressionResults(container, regression);

            } catch (error) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-4 mb-4';
                errorDiv.innerHTML = `<p class="text-red-800">${yCol} 多元回归分析失败: ${error.message}</p>`;
                container.appendChild(errorDiv);
            }
        });
    }

    /**
     * 创建增强回归分析结果展示
     * @param {HTMLElement} container - 容器元素
     * @param {Object} regression - 回归分析结果
     * @param {string} xCol - 自变量名
     * @param {string} yCol - 因变量名
     */
    createEnhancedRegressionResults(container, regression, xCol, yCol) {
        // 生成真正唯一的标识符，使用时间戳确保唯一性
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 11);
        const uniqueId = `${xCol}-${yCol}-${timestamp}-${randomId}`.replace(/[^a-zA-Z0-9\-]/g, '-');


        // 创建主容器
        const mainDiv = document.createElement('div');
        mainDiv.className = 'bg-white border rounded-lg p-4 mb-6';

        // 标题
        const title = document.createElement('h4');
        title.className = 'text-lg font-semibold text-gray-900 mb-4 border-b pb-2';
        title.textContent = `${yCol} ~ ${xCol} 增强回归分析`;
        mainDiv.appendChild(title);

        // 模型摘要
        const modelSummary = this.createModelSummaryTable(regression, uniqueId);
        mainDiv.appendChild(modelSummary);

        // 系数汇总表
        const coefficientsTable = this.createCoefficientsTable(regression, uniqueId);
        mainDiv.appendChild(coefficientsTable);

        // 方差分析表
        const anovaTable = this.createAnovaTable(regression, uniqueId);
        mainDiv.appendChild(anovaTable);

        // 残差分析
        const residualAnalysis = this.createResidualAnalysisSection(regression, xCol, yCol, uniqueId);
        mainDiv.appendChild(residualAnalysis);

        // 模型解释
        const interpretation = document.createElement('div');
        interpretation.className = 'mt-4 p-3 bg-blue-50 rounded-lg';
        interpretation.innerHTML = `
            <h5 class="font-medium text-blue-900 mb-2">模型解释</h5>
            <p class="text-blue-800 text-sm">${regression.interpretation}</p>
        `;
        mainDiv.appendChild(interpretation);

        // 个性化解释
        const personalizedInterpretation = document.createElement('div');
        personalizedInterpretation.innerHTML = this.generatePersonalizedInterpretation(regression);
        mainDiv.appendChild(personalizedInterpretation);

        container.appendChild(mainDiv);
    }

    /**
     * 创建模型摘要表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 模型摘要表
     */
    createModelSummaryTable(regression, uniqueId) {
        const container = document.createElement('div');
        container.className = 'mb-4';

        // 标题和说明按钮
        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '模型摘要 (Model Summary)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `model-summary-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        // 使用全局函数避免this上下文问题
        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            // 模型摘要按钮点击处理
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        const summaryData = [
            ['R²', regression.modelSummary.rSquared.toFixed(4), this.getRSquaredInterpretation(regression.modelSummary.rSquared)],
            ['调整R²', regression.modelSummary.adjustedRSquared.toFixed(4), this.getAdjustedRSquaredInterpretation(regression.modelSummary.adjustedRSquared)],
            ['标准误', regression.modelSummary.standardError.toFixed(4), this.getStandardErrorInterpretation(regression.modelSummary.standardError)],
            ['F统计量', regression.modelSummary.fStatistic.toFixed(4), this.getFStatisticInterpretation(regression.modelSummary.fStatistic, regression.modelSummary.fPValue)],
            ['F检验p值', regression.modelSummary.fPValue < 0.001 ? '<0.001' : regression.modelSummary.fPValue.toFixed(3), this.getPValueInterpretation(regression.modelSummary.fPValue)],
            ['自由度', regression.modelSummary.degreesOfFreedom, '模型的自由度（样本量-参数个数-1）'],
            ['样本量', regression.n, '参与分析的数据点个数']
        ];

        summaryData.forEach(([label, value, interpretation]) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="border border-gray-300 px-3 py-2 bg-gray-50 font-medium">${label}</td>
                <td class="border border-gray-300 px-3 py-2">${value}</td>
                <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">${interpretation}</td>
            `;
            table.appendChild(row);
        });

        // 详细解释区域
        const explanationDiv = document.createElement('div');
        explanationDiv.id = explanationId;
        explanationDiv.className = 'hidden mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm';
        explanationDiv.innerHTML = this.getModelSummaryDetailedExplanation(regression);

        container.appendChild(titleContainer);
        container.appendChild(table);
        container.appendChild(explanationDiv);

        return container;
    }

    /**
     * 创建系数汇总表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 系数表
     */
    createCoefficientsTable(regression, uniqueId) {
        const container = document.createElement('div');
        container.className = 'mb-4';

        // 标题和说明按钮
        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '系数汇总 (Coefficients Summary)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `coefficients-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        // 使用全局函数避免this上下文问题
        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            // 系数按钮点击处理
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        // 表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr class="bg-gray-100">
                <th class="border border-gray-300 px-3 py-2 text-left">系数</th>
                <th class="border border-gray-300 px-3 py-2 text-center">估计值</th>
                <th class="border border-gray-300 px-3 py-2 text-center">标准误</th>
                <th class="border border-gray-300 px-3 py-2 text-center">t值</th>
                <th class="border border-gray-300 px-3 py-2 text-center">p值</th>
                <th class="border border-gray-300 px-3 py-2 text-center">95%置信区间</th>
                <th class="border border-gray-300 px-3 py-2 text-center">解释</th>
            </tr>
        `;
        table.appendChild(thead);

        // 表体
        const tbody = document.createElement('tbody');

        // 截距行
        const interceptRow = document.createElement('tr');
        const interceptCI = `[${regression.coefficients.intercept.confidenceInterval[0].toFixed(4)}, ${regression.coefficients.intercept.confidenceInterval[1].toFixed(4)}]`;
        const interceptInterpretation = this.getInterceptInterpretation(regression.coefficients.intercept.estimate, regression.yName);
        interceptRow.innerHTML = `
            <td class="border border-gray-300 px-3 py-2 font-medium">截距</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.intercept.estimate.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.intercept.standardError.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.intercept.tValue.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.intercept.pValue < 0.001 ? '<0.001' : regression.coefficients.intercept.pValue.toFixed(3)} ${this.getPValueInterpretation(regression.coefficients.intercept.pValue)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${interceptCI}</td>
            <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">${interceptInterpretation}</td>
        `;
        tbody.appendChild(interceptRow);

        // 斜率行
        const slopeRow = document.createElement('tr');
        const slopeCI = `[${regression.coefficients.slope.confidenceInterval[0].toFixed(4)}, ${regression.coefficients.slope.confidenceInterval[1].toFixed(4)}]`;
        const slopeInterpretation = this.getSlopeInterpretation(regression.coefficients.slope.estimate, regression.xName, regression.yName);
        slopeRow.innerHTML = `
            <td class="border border-gray-300 px-3 py-2 font-medium">${regression.xName}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.slope.estimate.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.slope.standardError.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.slope.tValue.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.coefficients.slope.pValue < 0.001 ? '<0.001' : regression.coefficients.slope.pValue.toFixed(3)} ${this.getPValueInterpretation(regression.coefficients.slope.pValue)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${slopeCI}</td>
            <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">${slopeInterpretation}</td>
        `;
        tbody.appendChild(slopeRow);

        table.appendChild(tbody);

        // 详细解释区域
        const explanationDiv = document.createElement('div');
        explanationDiv.id = explanationId;
        explanationDiv.className = 'hidden mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm';
        explanationDiv.innerHTML = this.getCoefficientsDetailedExplanation(regression);

        container.appendChild(titleContainer);
        container.appendChild(table);
        container.appendChild(explanationDiv);

        return container;
    }

    /**
     * 创建方差分析表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 方差分析表
     */
    createAnovaTable(regression, uniqueId) {
        const container = document.createElement('div');
        container.className = 'mb-4';

        // 标题和说明按钮
        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '方差分析 (ANOVA)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `anova-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        // 使用全局函数避免this上下文问题
        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            // ANOVA按钮点击处理
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        // 表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr class="bg-gray-100">
                <th class="border border-gray-300 px-3 py-2 text-left">来源</th>
                <th class="border border-gray-300 px-3 py-2 text-center">自由度</th>
                <th class="border border-gray-300 px-3 py-2 text-center">平方和</th>
                <th class="border border-gray-300 px-3 py-2 text-center">均方</th>
                <th class="border border-gray-300 px-3 py-2 text-center">F值</th>
                <th class="border border-gray-300 px-3 py-2 text-center">p值</th>
                <th class="border border-gray-300 px-3 py-2 text-center">含义</th>
            </tr>
        `;
        table.appendChild(thead);

        // 表体
        const tbody = document.createElement('tbody');

        // 回归行
        const regressionRow = document.createElement('tr');
        regressionRow.innerHTML = `
            <td class="border border-gray-300 px-3 py-2 font-medium">回归</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.regression.df}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.regression.sumOfSquares.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.regression.meanSquare.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.regression.fValue.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.regression.pValue < 0.001 ? '<0.001' : regression.anova.regression.pValue.toFixed(3)}</td>
            <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">模型能解释的变异</td>
        `;
        tbody.appendChild(regressionRow);

        // 残差行
        const residualRow = document.createElement('tr');
        residualRow.innerHTML = `
            <td class="border border-gray-300 px-3 py-2 font-medium">残差</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.residual.df}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.residual.sumOfSquares.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.residual.meanSquare.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">-</td>
            <td class="border border-gray-300 px-3 py-2 text-center">-</td>
            <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">模型无法解释的变异</td>
        `;
        tbody.appendChild(residualRow);

        // 总计行
        const totalRow = document.createElement('tr');
        totalRow.innerHTML = `
            <td class="border border-gray-300 px-3 py-2 font-medium">总计</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.total.df}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">${regression.anova.total.sumOfSquares.toFixed(4)}</td>
            <td class="border border-gray-300 px-3 py-2 text-center">-</td>
            <td class="border border-gray-300 px-3 py-2 text-center">-</td>
            <td class="border border-gray-300 px-3 py-2 text-center">-</td>
            <td class="border border-gray-300 px-3 py-2 text-xs text-gray-600">数据的总变异</td>
        `;
        tbody.appendChild(totalRow);

        table.appendChild(tbody);

        // 详细解释区域
        const explanationDiv = document.createElement('div');
        explanationDiv.id = explanationId;
        explanationDiv.className = 'hidden mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm';
        explanationDiv.innerHTML = this.getAnovaDetailedExplanation(regression);

        container.appendChild(titleContainer);
        container.appendChild(table);
        container.appendChild(explanationDiv);

        return container;
    }

    /**
     * 获取选中的分析选项
     * @returns {Array} 选中的分析选项
     */
    getSelectedAnalyses() {
        const analyses = [];

        if (document.getElementById('basic-stats').checked) {
            analyses.push('basic-stats');
        }
        if (document.getElementById('frequency-dist').checked) {
            analyses.push('frequency-dist');
        }
        if (document.getElementById('correlation').checked) {
            analyses.push('correlation');
        }
        if (document.getElementById('t-test').checked) {
            analyses.push('t-test');
        }
        if (document.getElementById('regression').checked) {
            analyses.push('regression');
        }

        return analyses;
    }

    /**
     * 创建残差分析部分
     * @param {Object} regression - 回归分析结果
     * @param {string} xCol - 自变量名
     * @param {string} yCol - 因变量名
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 残差分析容器
     */
    createResidualAnalysisSection(regression, xCol, yCol, uniqueId) {
        const container = document.createElement('div');
        container.className = 'mb-4';

        // 标题和说明按钮
        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-3';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '残差分析 (Residual Analysis)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `residual-explanation-${uniqueId}`;
        // 创建残差分析解释按钮
        helpButton.setAttribute('data-target', explanationId);

        // 使用全局函数避免this上下文问题
        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            // 残差按钮点击处理
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);
        container.appendChild(titleContainer);

        // 残差解释说明
        const residualExplanation = document.createElement('div');
        residualExplanation.className = 'mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm';
        residualExplanation.innerHTML = `
            <p class="text-blue-800">
                <strong>💡 什么是残差？</strong> 残差是实际观测值与模型预测值的差异，用来检验模型的拟合效果。
                理想情况下，残差应该随机分布且接近正态分布。
            </p>
        `;
        container.appendChild(residualExplanation);

        // 残差统计摘要
        const residualStats = document.createElement('div');
        residualStats.className = 'mb-4 p-3 bg-gray-50 rounded-lg';
        residualStats.innerHTML = `
            <h6 class="font-medium text-gray-800 mb-2">残差统计摘要</h6>
            <div class="grid grid-cols-2 sm:grid-cols-4 gap-3 text-sm">
                <div>
                    <span class="text-gray-600">均值:</span>
                    <span class="font-medium">${regression.residualAnalysis.statistics.mean.toFixed(6)}</span>
                    <div class="text-xs text-gray-500">应接近0</div>
                </div>
                <div>
                    <span class="text-gray-600">标准差:</span>
                    <span class="font-medium">${regression.residualAnalysis.statistics.standardDeviation.toFixed(4)}</span>
                    <div class="text-xs text-gray-500">误差大小</div>
                </div>
                <div>
                    <span class="text-gray-600">最小值:</span>
                    <span class="font-medium">${regression.residualAnalysis.statistics.min.toFixed(4)}</span>
                    <div class="text-xs text-gray-500">最大负偏差</div>
                </div>
                <div>
                    <span class="text-gray-600">最大值:</span>
                    <span class="font-medium">${regression.residualAnalysis.statistics.max.toFixed(4)}</span>
                    <div class="text-xs text-gray-500">最大正偏差</div>
                </div>
            </div>
        `;
        container.appendChild(residualStats);

        // 诊断信息
        const diagnostics = document.createElement('div');
        diagnostics.className = 'mb-4 p-3 rounded-lg';

        let diagnosticClass = 'bg-green-50 border border-green-200';
        let diagnosticColor = 'text-green-800';
        let diagnosticIcon = '✓';

        const issues = [];
        if (!regression.residualAnalysis.diagnostics.isNormal) {
            issues.push('残差可能不服从正态分布');
        }
        if (regression.residualAnalysis.diagnostics.hasOutliers) {
            issues.push(`检测到${regression.residualAnalysis.outliers.length}个异常值`);
        }
        if (regression.residualAnalysis.diagnostics.hasAutocorrelation) {
            issues.push('可能存在自相关问题');
        }

        if (issues.length > 0) {
            diagnosticClass = 'bg-yellow-50 border border-yellow-200';
            diagnosticColor = 'text-yellow-800';
            diagnosticIcon = '⚠';
        }

        diagnostics.className += ' ' + diagnosticClass;
        diagnostics.innerHTML = `
            <h6 class="font-medium ${diagnosticColor} mb-2">${diagnosticIcon} 模型诊断</h6>
            <div class="${diagnosticColor} text-sm">
                ${issues.length === 0 ?
                    '<p>模型假设检验通过，残差分析结果良好。</p>' :
                    '<ul class="list-disc list-inside space-y-1">' + issues.map(issue => `<li>${issue}</li>`).join('') + '</ul>'
                }
                <div class="mt-2 grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                    <div>Durbin-Watson: ${regression.residualAnalysis.statistics.durbinWatson.toFixed(3)}</div>
                    <div>正态性p值: ${regression.residualAnalysis.normalityTest.pValue.toFixed(3)}</div>
                </div>
            </div>
        `;
        container.appendChild(diagnostics);

        // 残差图表
        const chartsContainer = document.createElement('div');
        chartsContainer.className = 'grid grid-cols-1 lg:grid-cols-2 gap-3';

        // 残差vs拟合值图
        const residualPlotContainer = window.chartRenderer.createChartContainer(
            `residual-plot-${xCol}-${yCol}`,
            '残差 vs 拟合值'
        );
        const residualCanvas = window.chartRenderer.createCanvas(`residual-canvas-${xCol}-${yCol}`);
        residualPlotContainer.canvasContainer.appendChild(residualCanvas);
        chartsContainer.appendChild(residualPlotContainer.container);

        // 标准化残差图
        const stdResidualPlotContainer = window.chartRenderer.createChartContainer(
            `std-residual-plot-${xCol}-${yCol}`,
            '标准化残差图'
        );
        const stdResidualCanvas = window.chartRenderer.createCanvas(`std-residual-canvas-${xCol}-${yCol}`);
        stdResidualPlotContainer.canvasContainer.appendChild(stdResidualCanvas);
        chartsContainer.appendChild(stdResidualPlotContainer.container);

        // Q-Q图
        const qqPlotContainer = window.chartRenderer.createChartContainer(
            `qq-plot-${xCol}-${yCol}`,
            '残差正态Q-Q图'
        );
        const qqCanvas = window.chartRenderer.createCanvas(`qq-canvas-${xCol}-${yCol}`);
        qqPlotContainer.canvasContainer.appendChild(qqCanvas);
        chartsContainer.appendChild(qqPlotContainer.container);

        // 带回归线的散点图
        const scatterPlotContainer = window.chartRenderer.createChartContainer(
            `scatter-plot-${xCol}-${yCol}`,
            `${yCol} vs ${xCol} (含回归线)`
        );
        const scatterCanvas = window.chartRenderer.createCanvas(`scatter-canvas-${xCol}-${yCol}`);
        scatterPlotContainer.canvasContainer.appendChild(scatterCanvas);
        chartsContainer.appendChild(scatterPlotContainer.container);

        container.appendChild(chartsContainer);

        // 详细解释区域
        const explanationDiv = document.createElement('div');
        explanationDiv.id = explanationId;
        // 创建残差分析解释区域
        explanationDiv.className = 'hidden mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg text-sm';
        explanationDiv.innerHTML = this.getResidualDetailedExplanation(regression);
        container.appendChild(explanationDiv);

        // 渲染图表 - 使用更长的延迟确保DOM元素完全创建
        setTimeout(() => {
            try {
                // 验证所有canvas元素是否存在
                const canvasIds = [
                    `residual-canvas-${xCol}-${yCol}`,
                    `std-residual-canvas-${xCol}-${yCol}`,
                    `qq-canvas-${xCol}-${yCol}`,
                    `scatter-canvas-${xCol}-${yCol}`
                ];

                const missingCanvases = canvasIds.filter(id => !document.getElementById(id));
                if (missingCanvases.length > 0) {
                    return;
                }

                // 渲染残差vs拟合值图
                window.chartRenderer.renderResidualPlot(
                    `residual-canvas-${xCol}-${yCol}`,
                    regression.data.fitted,
                    regression.data.residuals
                );

                // 渲染标准化残差图
                window.chartRenderer.renderStandardizedResidualPlot(
                    `std-residual-canvas-${xCol}-${yCol}`,
                    regression.data.fitted,
                    regression.residualAnalysis.standardizedResiduals
                );

                // 渲染Q-Q图
                window.chartRenderer.renderQQPlot(
                    `qq-canvas-${xCol}-${yCol}`,
                    regression.data.residuals
                );

                // 渲染散点图
                window.chartRenderer.renderScatterPlot(
                    `scatter-canvas-${xCol}-${yCol}`,
                    regression.data.x,
                    regression.data.y,
                    `${yCol} vs ${xCol}`,
                    xCol,
                    yCol,
                    { slope: regression.coefficients.slope.estimate, intercept: regression.coefficients.intercept.estimate }
                );

            } catch (error) {
                // 渲染残差分析图表时出错
                // 显示错误信息给用户
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-50 border border-red-200 rounded-md p-3 mt-3';
                errorDiv.innerHTML = `
                    <p class="text-red-800 text-sm">
                        <strong>图表渲染错误:</strong> ${error.message}
                    </p>
                `;
                container.appendChild(errorDiv);
            }
        }, 300); // 增加延迟时间

        return container;
    }

    /**
     * 创建多元回归分析结果展示
     * @param {HTMLElement} container - 容器元素
     * @param {Object} regression - 多元回归分析结果
     */
    createMultipleRegressionResults(container, regression) {
        // 生成唯一标识符
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 11);
        const uniqueId = `multiple-regression-${timestamp}-${randomId}`;

        // 创建主容器
        const mainDiv = document.createElement('div');
        mainDiv.className = 'bg-white border rounded-lg p-4 mb-6';

        // 标题
        const title = document.createElement('h4');
        title.className = 'text-lg font-semibold text-gray-900 mb-4 border-b pb-2';

        if (regression.numVariables === 1) {
            title.textContent = `${regression.yName} ~ ${regression.xNames[0]} 简单线性回归分析`;
        } else {
            title.textContent = `${regression.yName} ~ ${regression.xNames.join(' + ')} 多元线性回归分析`;
        }
        mainDiv.appendChild(title);

        // 回归方程
        const equationDiv = document.createElement('div');
        equationDiv.className = 'bg-blue-50 border border-blue-200 rounded-md p-3 mb-4';
        equationDiv.innerHTML = `
            <h5 class="font-medium text-blue-900 mb-2">📊 回归方程</h5>
            <p class="text-blue-800 font-mono text-sm">${regression.equation}</p>
        `;
        mainDiv.appendChild(equationDiv);

        // 模型摘要表
        mainDiv.appendChild(this.createMultipleRegressionModelSummary(regression, uniqueId));

        // 系数表
        mainDiv.appendChild(this.createMultipleRegressionCoefficientsTable(regression, uniqueId));

        // 方差分析表
        mainDiv.appendChild(this.createMultipleRegressionAnovaTable(regression, uniqueId));

        // 残差分析
        if (regression.residualAnalysis) {
            mainDiv.appendChild(this.createMultipleRegressionResidualAnalysis(regression, uniqueId));
        }

        // 诊断图表
        mainDiv.appendChild(this.createMultipleRegressionDiagnosticCharts(regression, uniqueId));

        container.appendChild(mainDiv);

        // 渲染图表（延迟执行确保DOM元素已创建）
        setTimeout(() => {
            this.renderMultipleRegressionCharts(regression, uniqueId);
        }, 300);

        return container;
    }

    /**
     * 创建多元回归模型摘要表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 模型摘要表元素
     */
    createMultipleRegressionModelSummary(regression, uniqueId) {
        const section = document.createElement('div');
        section.className = 'mb-4';

        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '模型摘要 (Model Summary)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `model-summary-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        table.innerHTML = `
            <thead>
                <tr class="bg-gray-50">
                    <th class="border border-gray-300 px-3 py-2 text-left">指标</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">数值</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">R² (决定系数)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.modelSummary.rSquared.toFixed(4)}</td>
                    <td class="border border-gray-300 px-3 py-2">${(regression.modelSummary.rSquared * 100).toFixed(1)}% 的变异被解释</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">调整R² (Adjusted R²)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.modelSummary.adjustedRSquared.toFixed(4)}</td>
                    <td class="border border-gray-300 px-3 py-2">${(regression.modelSummary.adjustedRSquared * 100).toFixed(1)}% (考虑变量数量)</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">标准误 (Std. Error)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.modelSummary.standardError.toFixed(4)}</td>
                    <td class="border border-gray-300 px-3 py-2">预测误差的标准差</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">F统计量 (F-statistic)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.modelSummary.fStatistic.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2">模型整体显著性检验</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">p值 (p-value)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.modelSummary.fPValue < 0.001 ? '<0.001' : regression.modelSummary.fPValue.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2">${regression.modelSummary.fPValue < 0.05 ? '模型显著' : '模型不显著'}</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">样本量 (N)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.n}</td>
                    <td class="border border-gray-300 px-3 py-2">有效观测数</td>
                </tr>
            </tbody>
        `;

        // 解释区域
        const explanation = document.createElement('div');
        explanation.id = explanationId;
        explanation.className = 'hidden mt-3 p-3 bg-green-50 border border-green-200 rounded-md';
        explanation.innerHTML = `
            <h6 class="font-medium text-green-900 mb-2">📚 模型摘要解释</h6>
            <div class="space-y-2 text-sm text-green-800">
                <p><strong>R²:</strong> 表示模型解释因变量变异的比例，越接近1越好</p>
                <p><strong>调整R²:</strong> 考虑了变量数量的影响，比R²更可靠</p>
                <p><strong>F统计量:</strong> 检验模型整体是否显著，配合p值判断</p>
                <p><strong>p值:</strong> 小于0.05表示模型在统计上显著</p>
            </div>
        `;

        section.appendChild(titleContainer);
        section.appendChild(table);
        section.appendChild(explanation);

        return section;
    }

    /**
     * 创建多元回归系数表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 系数表元素
     */
    createMultipleRegressionCoefficientsTable(regression, uniqueId) {
        const section = document.createElement('div');
        section.className = 'mb-4';

        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '回归系数 (Coefficients)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `coefficients-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        let tableHTML = `
            <thead>
                <tr class="bg-gray-50">
                    <th class="border border-gray-300 px-3 py-2 text-left">变量</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">系数</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">标准误</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">t值</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">p值</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">95%置信区间</th>
                </tr>
            </thead>
            <tbody>
        `;

        // 截距项
        const intercept = regression.coefficients.intercept;
        tableHTML += `
            <tr>
                <td class="border border-gray-300 px-3 py-2 font-medium">截距 (Intercept)</td>
                <td class="border border-gray-300 px-3 py-2 text-center font-mono">${intercept.estimate.toFixed(4)}</td>
                <td class="border border-gray-300 px-3 py-2 text-center font-mono">${intercept.standardError.toFixed(4)}</td>
                <td class="border border-gray-300 px-3 py-2 text-center font-mono">${intercept.tValue.toFixed(3)}</td>
                <td class="border border-gray-300 px-3 py-2 text-center font-mono ${intercept.pValue < 0.05 ? 'text-red-600 font-bold' : ''}">${intercept.pValue < 0.001 ? '<0.001' : intercept.pValue.toFixed(3)}</td>
                <td class="border border-gray-300 px-3 py-2 text-center font-mono">[${intercept.confidenceInterval[0].toFixed(3)}, ${intercept.confidenceInterval[1].toFixed(3)}]</td>
            </tr>
        `;

        // 各个自变量
        regression.coefficients.variables.forEach(variable => {
            tableHTML += `
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">${variable.name}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${variable.estimate.toFixed(4)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${variable.standardError.toFixed(4)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${variable.tValue.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono ${variable.pValue < 0.05 ? 'text-red-600 font-bold' : ''}">${variable.pValue < 0.001 ? '<0.001' : variable.pValue.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">[${variable.confidenceInterval[0].toFixed(3)}, ${variable.confidenceInterval[1].toFixed(3)}]</td>
                </tr>
            `;
        });

        tableHTML += `
            </tbody>
        `;

        table.innerHTML = tableHTML;

        // 解释区域
        const explanation = document.createElement('div');
        explanation.id = explanationId;
        explanation.className = 'hidden mt-3 p-3 bg-green-50 border border-green-200 rounded-md';
        explanation.innerHTML = `
            <h6 class="font-medium text-green-900 mb-2">📚 回归系数解释</h6>
            <div class="space-y-2 text-sm text-green-800">
                <p><strong>系数:</strong> 表示自变量变化1个单位时，因变量的平均变化量</p>
                <p><strong>标准误:</strong> 系数估计的标准差，越小越精确</p>
                <p><strong>t值:</strong> 系数/标准误，用于检验系数是否显著</p>
                <p><strong>p值:</strong> 小于0.05表示该变量对因变量有显著影响</p>
                <p><strong>置信区间:</strong> 系数真实值的可能范围</p>
            </div>
        `;

        section.appendChild(titleContainer);
        section.appendChild(table);
        section.appendChild(explanation);

        return section;
    }

    /**
     * 创建多元回归方差分析表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 方差分析表元素
     */
    createMultipleRegressionAnovaTable(regression, uniqueId) {
        const section = document.createElement('div');
        section.className = 'mb-4';

        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-2';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '方差分析 (ANOVA)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 查看解释';
        const explanationId = `anova-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        const table = document.createElement('table');
        table.className = 'w-full text-sm border-collapse border border-gray-300';

        table.innerHTML = `
            <thead>
                <tr class="bg-gray-50">
                    <th class="border border-gray-300 px-3 py-2 text-left">来源</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">自由度</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">平方和</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">均方</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">F值</th>
                    <th class="border border-gray-300 px-3 py-2 text-center">p值</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">回归 (Regression)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.regression.df}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.regression.sumOfSquares.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.regression.meanSquare.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.regression.fValue.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono ${regression.anova.regression.pValue < 0.05 ? 'text-red-600 font-bold' : ''}">${regression.anova.regression.pValue < 0.001 ? '<0.001' : regression.anova.regression.pValue.toFixed(3)}</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">残差 (Residual)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.residual.df}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.residual.sumOfSquares.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.residual.meanSquare.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center">-</td>
                    <td class="border border-gray-300 px-3 py-2 text-center">-</td>
                </tr>
                <tr>
                    <td class="border border-gray-300 px-3 py-2 font-medium">总计 (Total)</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.total.df}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center font-mono">${regression.anova.total.sumOfSquares.toFixed(3)}</td>
                    <td class="border border-gray-300 px-3 py-2 text-center">-</td>
                    <td class="border border-gray-300 px-3 py-2 text-center">-</td>
                    <td class="border border-gray-300 px-3 py-2 text-center">-</td>
                </tr>
            </tbody>
        `;

        // 解释区域
        const explanation = document.createElement('div');
        explanation.id = explanationId;
        explanation.className = 'hidden mt-3 p-3 bg-green-50 border border-green-200 rounded-md';
        explanation.innerHTML = `
            <h6 class="font-medium text-green-900 mb-2">📚 方差分析解释</h6>
            <div class="space-y-2 text-sm text-green-800">
                <p><strong>回归平方和:</strong> 模型解释的变异</p>
                <p><strong>残差平方和:</strong> 模型未解释的变异</p>
                <p><strong>F值:</strong> 回归均方/残差均方，检验模型整体显著性</p>
                <p><strong>p值:</strong> 小于0.05表示模型整体显著</p>
            </div>
        `;

        section.appendChild(titleContainer);
        section.appendChild(table);
        section.appendChild(explanation);

        return section;
    }

    /**
     * 创建多元回归残差分析部分
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 残差分析元素
     */
    createMultipleRegressionResidualAnalysis(regression, uniqueId) {
        const section = document.createElement('div');
        section.className = 'mb-4';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900 mb-2';
        title.textContent = '残差分析 (Residual Analysis)';

        const residualStats = regression.residualAnalysis.statistics;
        const diagnostics = regression.residualAnalysis.diagnostics;

        const content = document.createElement('div');
        content.className = 'grid grid-cols-1 md:grid-cols-2 gap-4';

        // 残差统计
        const statsDiv = document.createElement('div');
        statsDiv.className = 'bg-gray-50 border border-gray-200 rounded-md p-3';
        statsDiv.innerHTML = `
            <h6 class="font-medium text-gray-900 mb-2">残差统计量</h6>
            <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                    <span>均值:</span>
                    <span class="font-mono">${residualStats.mean.toFixed(6)}</span>
                </div>
                <div class="flex justify-between">
                    <span>标准差:</span>
                    <span class="font-mono">${residualStats.standardDeviation.toFixed(4)}</span>
                </div>
                <div class="flex justify-between">
                    <span>最小值:</span>
                    <span class="font-mono">${residualStats.min.toFixed(4)}</span>
                </div>
                <div class="flex justify-between">
                    <span>最大值:</span>
                    <span class="font-mono">${residualStats.max.toFixed(4)}</span>
                </div>
                <div class="flex justify-between">
                    <span>DW统计量:</span>
                    <span class="font-mono">${residualStats.durbinWatson.toFixed(4)}</span>
                </div>
            </div>
        `;

        // 诊断结果
        const diagnosticsDiv = document.createElement('div');
        diagnosticsDiv.className = 'bg-gray-50 border border-gray-200 rounded-md p-3';
        diagnosticsDiv.innerHTML = `
            <h6 class="font-medium text-gray-900 mb-2">模型诊断</h6>
            <div class="space-y-1 text-sm">
                <div class="flex justify-between">
                    <span>正态性:</span>
                    <span class="${diagnostics.isNormal ? 'text-green-600' : 'text-red-600'}">${diagnostics.isNormal ? '通过' : '不通过'}</span>
                </div>
                <div class="flex justify-between">
                    <span>异常值:</span>
                    <span class="${diagnostics.hasOutliers ? 'text-red-600' : 'text-green-600'}">${diagnostics.hasOutliers ? `发现${regression.residualAnalysis.outliers.length}个` : '无'}</span>
                </div>
                <div class="flex justify-between">
                    <span>自相关:</span>
                    <span class="${diagnostics.hasAutocorrelation ? 'text-red-600' : 'text-green-600'}">${diagnostics.hasAutocorrelation ? '存在' : '无'}</span>
                </div>
            </div>
        `;

        content.appendChild(statsDiv);
        content.appendChild(diagnosticsDiv);

        section.appendChild(title);
        section.appendChild(content);

        return section;
    }

    /**
     * 创建多元回归诊断图表容器
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 图表容器元素
     */
    createMultipleRegressionDiagnosticCharts(regression, uniqueId) {
        const section = document.createElement('div');
        section.className = 'mb-6';

        const titleContainer = document.createElement('div');
        titleContainer.className = 'flex items-center justify-between mb-4';

        const title = document.createElement('h5');
        title.className = 'font-medium text-gray-900';
        title.textContent = '可视化诊断图表 (Diagnostic Charts)';

        const helpButton = document.createElement('button');
        helpButton.className = 'text-blue-600 hover:text-blue-800 text-sm cursor-pointer';
        helpButton.innerHTML = '📖 图表解读指南';
        const explanationId = `charts-explanation-${uniqueId}`;
        helpButton.setAttribute('data-target', explanationId);

        helpButton.addEventListener('click', function(e) {
            e.preventDefault();
            window.toggleExplanationGlobal(explanationId);
        });

        titleContainer.appendChild(title);
        titleContainer.appendChild(helpButton);

        // 图表解读指南
        const explanation = document.createElement('div');
        explanation.id = explanationId;
        explanation.className = 'hidden mb-4 p-4 bg-green-50 border border-green-200 rounded-md';
        explanation.innerHTML = `
            <h6 class="font-medium text-green-900 mb-3">📚 图表解读指南</h6>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
                <div>
                    <h7 class="font-medium">残差图表：</h7>
                    <ul class="mt-1 space-y-1 list-disc list-inside">
                        <li>残差应随机分布在零线周围</li>
                        <li>无明显模式表示模型假设成立</li>
                        <li>漏斗形表示异方差性问题</li>
                    </ul>
                </div>
                <div>
                    <h7 class="font-medium">Q-Q图：</h7>
                    <ul class="mt-1 space-y-1 list-disc list-inside">
                        <li>点越接近直线，残差越接近正态分布</li>
                        <li>两端偏离表示尾部偏重</li>
                        <li>S形曲线表示偏度问题</li>
                    </ul>
                </div>
                <div>
                    <h7 class="font-medium">预测vs实际：</h7>
                    <ul class="mt-1 space-y-1 list-disc list-inside">
                        <li>点越接近对角线，预测越准确</li>
                        <li>R²值反映整体拟合程度</li>
                        <li>离群点需要特别关注</li>
                    </ul>
                </div>
                <div>
                    <h7 class="font-medium">系数重要性：</h7>
                    <ul class="mt-1 space-y-1 list-disc list-inside">
                        <li>条形长度表示影响大小</li>
                        <li>颜色表示统计显著性</li>
                        <li>帮助识别关键变量</li>
                    </ul>
                </div>
            </div>
        `;

        // 加载状态提示
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = `charts-loading-${uniqueId}`;
        loadingIndicator.className = 'text-center py-8 text-gray-500';
        loadingIndicator.innerHTML = `
            <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                正在生成图表...
            </div>
        `;

        // 主图表容器
        const chartsContainer = document.createElement('div');
        chartsContainer.id = `charts-container-${uniqueId}`;
        chartsContainer.className = 'hidden space-y-6';

        // 第一行：核心诊断图表
        const coreChartsRow = document.createElement('div');
        coreChartsRow.className = 'grid grid-cols-1 lg:grid-cols-2 gap-4';

        // 残差vs拟合值图
        const residualChart = this.createChartCard(
            `residual-fitted-chart-${uniqueId}`,
            '残差 vs 拟合值',
            '检验模型假设和异方差性',
            uniqueId
        );

        // Q-Q图
        const qqChart = this.createChartCard(
            `qq-chart-${uniqueId}`,
            'Q-Q图 (正态性检验)',
            '检验残差的正态分布假设',
            uniqueId
        );

        coreChartsRow.appendChild(residualChart);
        coreChartsRow.appendChild(qqChart);

        // 第二行：预测效果图表
        const predictionChartsRow = document.createElement('div');
        predictionChartsRow.className = 'grid grid-cols-1 lg:grid-cols-2 gap-4';

        // 预测vs实际值图
        const predictionChart = this.createChartCard(
            `prediction-actual-chart-${uniqueId}`,
            '预测值 vs 实际值',
            '评估模型预测准确性',
            uniqueId
        );

        // 残差分布直方图
        const residualHistChart = this.createChartCard(
            `residual-histogram-${uniqueId}`,
            '残差分布直方图',
            '检验残差的分布特征',
            uniqueId
        );

        predictionChartsRow.appendChild(predictionChart);
        predictionChartsRow.appendChild(residualHistChart);

        // 第三行：系数分析图表
        const coefficientsChartsRow = document.createElement('div');
        coefficientsChartsRow.className = 'grid grid-cols-1 gap-4';

        // 系数重要性图
        const coefficientsChart = this.createChartCard(
            `coefficients-importance-chart-${uniqueId}`,
            '系数重要性分析',
            '各变量对因变量的影响程度和统计显著性',
            uniqueId,
            'extra-large'
        );

        coefficientsChartsRow.appendChild(coefficientsChart);

        // 第四行：高级诊断图表（仅多变量时显示）
        let advancedChartsRow = null;
        if (regression.numVariables > 1) {
            advancedChartsRow = document.createElement('div');
            advancedChartsRow.className = 'grid grid-cols-1 lg:grid-cols-2 gap-4';

            // Cook距离图
            const cookChart = this.createChartCard(
                `cook-distance-chart-${uniqueId}`,
                'Cook距离图',
                '识别影响点和异常值',
                uniqueId
            );

            // 杠杆值图
            const leverageChart = this.createChartCard(
                `leverage-chart-${uniqueId}`,
                '杠杆值图',
                '检测高杠杆点',
                uniqueId
            );

            advancedChartsRow.appendChild(cookChart);
            advancedChartsRow.appendChild(leverageChart);
        }

        // 组装图表容器
        chartsContainer.appendChild(coreChartsRow);
        chartsContainer.appendChild(predictionChartsRow);
        chartsContainer.appendChild(coefficientsChartsRow);
        if (advancedChartsRow) {
            chartsContainer.appendChild(advancedChartsRow);
        }

        // 组装主容器
        section.appendChild(titleContainer);
        section.appendChild(explanation);
        section.appendChild(loadingIndicator);
        section.appendChild(chartsContainer);

        return section;
    }

    /**
     * 创建图表卡片
     * @param {string} chartId - 图表ID
     * @param {string} title - 图表标题
     * @param {string} description - 图表描述
     * @param {string} uniqueId - 唯一标识符
     * @param {string} size - 尺寸 ('normal' | 'full' | 'extra-large')
     * @returns {HTMLElement} 图表卡片元素
     */
    createChartCard(chartId, title, description, uniqueId, size = 'normal') {
        const card = document.createElement('div');
        card.className = 'bg-white border border-gray-200 rounded-lg p-4 shadow-sm';

        const header = document.createElement('div');
        header.className = 'flex items-center justify-between mb-3';

        const titleContainer = document.createElement('div');

        const titleElement = document.createElement('h6');
        titleElement.className = 'font-medium text-gray-900 text-sm';
        titleElement.textContent = title;

        const descElement = document.createElement('p');
        descElement.className = 'text-xs text-gray-500 mt-1';
        descElement.textContent = description;

        titleContainer.appendChild(titleElement);
        titleContainer.appendChild(descElement);

        const exportButton = document.createElement('button');
        exportButton.className = 'text-gray-400 hover:text-gray-600 text-xs px-2 py-1 rounded border border-gray-200 hover:border-gray-300 transition-colors';
        exportButton.innerHTML = '📥 导出';
        exportButton.onclick = () => this.exportChart(chartId);

        header.appendChild(titleContainer);
        header.appendChild(exportButton);

        const chartContainer = document.createElement('div');
        chartContainer.id = chartId;

        // 根据尺寸设置不同的高度
        let heightClass;
        switch (size) {
            case 'extra-large':
                heightClass = 'w-full min-h-96'; // 最小384px，可自适应
                break;
            case 'full':
                heightClass = 'w-full h-80'; // 320px
                break;
            default:
                heightClass = 'w-full h-64'; // 256px
        }

        chartContainer.className = heightClass;

        card.appendChild(header);
        card.appendChild(chartContainer);

        return card;
    }

    /**
     * 导出图表为PNG
     * @param {string} chartId - 图表ID
     */
    exportChart(chartId) {
        try {
            const chartElement = document.getElementById(chartId);
            if (!chartElement) {
                // 图表未找到
                return;
            }

            // 使用Plotly的导出功能
            if (window.Plotly && chartElement.data) {
                Plotly.downloadImage(chartId, {
                    format: 'png',
                    width: 800,
                    height: 600,
                    filename: `regression_chart_${chartId}`
                });
            } else {
                // 图表导出功能暂不可用
            }
        } catch (error) {
            // 导出图表失败
        }
    }

    /**
     * 渲染多元回归图表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderMultipleRegressionCharts(regression, uniqueId) {
        try {
            // 隐藏加载指示器，显示图表容器
            const loadingElement = document.getElementById(`charts-loading-${uniqueId}`);
            const chartsContainer = document.getElementById(`charts-container-${uniqueId}`);

            if (loadingElement) loadingElement.classList.add('hidden');
            if (chartsContainer) chartsContainer.classList.remove('hidden');

            // 核心诊断图表
            this.renderResidualFittedChart(regression, uniqueId);
            this.renderQQChart(regression, uniqueId);

            // 预测效果图表
            this.renderPredictionActualChart(regression, uniqueId);
            this.renderResidualHistogram(regression, uniqueId);

            // 系数重要性图表
            this.renderCoefficientsImportanceChart(regression, uniqueId);

            // 高级诊断图表（仅多变量时）
            if (regression.numVariables > 1) {
                this.renderCookDistanceChart(regression, uniqueId);
                this.renderLeverageChart(regression, uniqueId);
            }

        } catch (error) {
            // 渲染多元回归图表失败

            // 显示错误信息
            const chartsContainer = document.getElementById(`charts-container-${uniqueId}`);
            if (chartsContainer) {
                chartsContainer.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <p>图表渲染失败: ${error.message}</p>
                        <p class="text-sm text-gray-500 mt-2">请检查数据格式或刷新页面重试</p>
                    </div>
                `;
                chartsContainer.classList.remove('hidden');
            }

            const loadingElement = document.getElementById(`charts-loading-${uniqueId}`);
            if (loadingElement) loadingElement.classList.add('hidden');
        }
    }

    /**
     * 渲染残差vs拟合值图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderResidualFittedChart(regression, uniqueId) {
        const chartId = `residual-fitted-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        // 计算残差的标准差用于着色
        const residuals = regression.data.residuals;
        const residualStd = Math.sqrt(residuals.reduce((sum, r) => sum + r * r, 0) / (residuals.length - 1));

        // 根据残差大小设置颜色
        const colors = residuals.map(r => {
            const absStandardized = Math.abs(r) / residualStd;
            if (absStandardized > 2.5) return 'rgba(239, 68, 68, 0.8)'; // 红色：异常值
            if (absStandardized > 2) return 'rgba(245, 158, 11, 0.8)'; // 橙色：可疑值
            return 'rgba(59, 130, 246, 0.6)'; // 蓝色：正常值
        });

        const data = [{
            x: regression.data.fitted,
            y: regression.data.residuals,
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: colors,
                size: 8,
                line: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    width: 1
                }
            },
            text: residuals.map((r, i) => `观测值 ${i + 1}<br>残差: ${r.toFixed(3)}<br>拟合值: ${regression.data.fitted[i].toFixed(3)}`),
            hovertemplate: '%{text}<extra></extra>',
            name: '残差点'
        }, {
            x: regression.data.fitted,
            y: new Array(regression.data.fitted.length).fill(0),
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(107, 114, 128, 0.8)',
                dash: 'dash',
                width: 2
            },
            hoverinfo: 'skip',
            name: '零线'
        }];

        // 添加±2标准差线
        const minFitted = Math.min(...regression.data.fitted);
        const maxFitted = Math.max(...regression.data.fitted);

        data.push({
            x: [minFitted, maxFitted],
            y: [2 * residualStd, 2 * residualStd],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.5)',
                dash: 'dot',
                width: 1
            },
            hoverinfo: 'skip',
            name: '+2σ'
        });

        data.push({
            x: [minFitted, maxFitted],
            y: [-2 * residualStd, -2 * residualStd],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.5)',
                dash: 'dot',
                width: 1
            },
            hoverinfo: 'skip',
            name: '-2σ'
        });

        const layout = {
            title: {
                text: '残差 vs 拟合值',
                font: { size: 14 }
            },
            xaxis: {
                title: '拟合值',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: '残差',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 50, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 渲染Q-Q图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderQQChart(regression, uniqueId) {
        const chartId = `qq-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        // 计算标准化残差
        const residuals = regression.data.residuals;
        const mean = residuals.reduce((sum, r) => sum + r, 0) / residuals.length;
        const std = Math.sqrt(residuals.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (residuals.length - 1));
        const standardizedResiduals = residuals.map(r => (r - mean) / std);

        // 排序并保存原始索引
        const indexedResiduals = standardizedResiduals.map((r, i) => ({ value: r, index: i }));
        indexedResiduals.sort((a, b) => a.value - b.value);
        const sortedResiduals = indexedResiduals.map(item => item.value);

        // 计算理论分位数
        const n = sortedResiduals.length;
        const theoreticalQuantiles = [];
        for (let i = 0; i < n; i++) {
            const p = (i + 0.5) / n;
            theoreticalQuantiles.push(this.normalInverse(p));
        }

        // 计算R²用于评估正态性
        const correlation = this.calculateCorrelation(theoreticalQuantiles, sortedResiduals);
        const rSquared = correlation * correlation;

        // 根据偏离程度设置颜色
        const colors = sortedResiduals.map((observed, i) => {
            const theoretical = theoreticalQuantiles[i];
            const deviation = Math.abs(observed - theoretical);
            if (deviation > 1.5) return 'rgba(239, 68, 68, 0.8)'; // 红色：严重偏离
            if (deviation > 1) return 'rgba(245, 158, 11, 0.8)'; // 橙色：中度偏离
            return 'rgba(59, 130, 246, 0.6)'; // 蓝色：正常
        });

        const data = [{
            x: theoreticalQuantiles,
            y: sortedResiduals,
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: colors,
                size: 8,
                line: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    width: 1
                }
            },
            text: sortedResiduals.map((r, i) =>
                `观测值: ${r.toFixed(3)}<br>理论值: ${theoreticalQuantiles[i].toFixed(3)}<br>偏差: ${Math.abs(r - theoreticalQuantiles[i]).toFixed(3)}`
            ),
            hovertemplate: '%{text}<extra></extra>',
            name: '观测分位数'
        }, {
            x: theoreticalQuantiles,
            y: theoreticalQuantiles,
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.8)',
                dash: 'dash',
                width: 2
            },
            hoverinfo: 'skip',
            name: '理论线'
        }];

        const layout = {
            title: {
                text: `Q-Q图 (正态性检验)<br><sub>相关系数 R = ${correlation.toFixed(3)}</sub>`,
                font: { size: 14 }
            },
            xaxis: {
                title: '理论分位数',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: '样本分位数',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 70, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white',
            annotations: [{
                x: 0.02,
                y: 0.98,
                xref: 'paper',
                yref: 'paper',
                text: `正态性评估: ${rSquared > 0.95 ? '优秀' : rSquared > 0.90 ? '良好' : rSquared > 0.80 ? '一般' : '较差'}`,
                showarrow: false,
                font: { size: 10, color: rSquared > 0.90 ? 'green' : rSquared > 0.80 ? 'orange' : 'red' },
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: 'rgba(0, 0, 0, 0.1)',
                borderwidth: 1
            }]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 渲染预测vs实际值图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderPredictionActualChart(regression, uniqueId) {
        const chartId = `prediction-actual-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        const actualValues = regression.data.y;
        const predictedValues = regression.data.fitted;

        // 计算预测误差
        const errors = actualValues.map((actual, i) => Math.abs(actual - predictedValues[i]));
        const maxError = Math.max(...errors);

        // 根据误差大小设置颜色
        const colors = errors.map(error => {
            const errorRatio = error / maxError;
            if (errorRatio > 0.8) return 'rgba(239, 68, 68, 0.8)'; // 红色：大误差
            if (errorRatio > 0.5) return 'rgba(245, 158, 11, 0.8)'; // 橙色：中等误差
            return 'rgba(34, 197, 94, 0.6)'; // 绿色：小误差
        });

        // 计算完美预测线的范围
        const minValue = Math.min(...actualValues, ...predictedValues);
        const maxValue = Math.max(...actualValues, ...predictedValues);
        const range = maxValue - minValue;
        const lineStart = minValue - range * 0.1;
        const lineEnd = maxValue + range * 0.1;

        const data = [{
            x: actualValues,
            y: predictedValues,
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: colors,
                size: 8,
                line: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    width: 1
                }
            },
            text: actualValues.map((actual, i) =>
                `实际值: ${actual.toFixed(3)}<br>预测值: ${predictedValues[i].toFixed(3)}<br>误差: ${errors[i].toFixed(3)}`
            ),
            hovertemplate: '%{text}<extra></extra>',
            name: '数据点'
        }, {
            x: [lineStart, lineEnd],
            y: [lineStart, lineEnd],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(107, 114, 128, 0.8)',
                dash: 'dash',
                width: 2
            },
            hoverinfo: 'skip',
            name: '完美预测线'
        }];

        const layout = {
            title: {
                text: `预测值 vs 实际值<br><sub>R² = ${regression.modelSummary.rSquared.toFixed(3)}</sub>`,
                font: { size: 14 }
            },
            xaxis: {
                title: '实际值',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: '预测值',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 70, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white',
            annotations: [{
                x: 0.02,
                y: 0.98,
                xref: 'paper',
                yref: 'paper',
                text: `预测准确性: ${regression.modelSummary.rSquared > 0.8 ? '优秀' : regression.modelSummary.rSquared > 0.6 ? '良好' : regression.modelSummary.rSquared > 0.4 ? '一般' : '较差'}`,
                showarrow: false,
                font: {
                    size: 10,
                    color: regression.modelSummary.rSquared > 0.6 ? 'green' : regression.modelSummary.rSquared > 0.4 ? 'orange' : 'red'
                },
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: 'rgba(0, 0, 0, 0.1)',
                borderwidth: 1
            }]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 渲染残差分布直方图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderResidualHistogram(regression, uniqueId) {
        const chartId = `residual-histogram-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        const residuals = regression.data.residuals;

        // 计算统计量
        const mean = residuals.reduce((sum, r) => sum + r, 0) / residuals.length;
        const std = Math.sqrt(residuals.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (residuals.length - 1));

        // 生成理论正态分布曲线
        const minRes = Math.min(...residuals);
        const maxRes = Math.max(...residuals);
        const range = maxRes - minRes;
        const xNormal = [];
        const yNormal = [];

        for (let i = 0; i <= 100; i++) {
            const x = minRes - range * 0.2 + (range * 1.4 * i) / 100;
            const y = (1 / (std * Math.sqrt(2 * Math.PI))) * Math.exp(-0.5 * Math.pow((x - mean) / std, 2));
            xNormal.push(x);
            yNormal.push(y);
        }

        const data = [{
            x: residuals,
            type: 'histogram',
            nbinsx: Math.min(20, Math.max(8, Math.floor(residuals.length / 5))),
            histnorm: 'probability density',
            marker: {
                color: 'rgba(59, 130, 246, 0.6)',
                line: {
                    color: 'rgba(59, 130, 246, 1)',
                    width: 1
                }
            },
            name: '残差分布'
        }, {
            x: xNormal,
            y: yNormal,
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.8)',
                width: 2
            },
            name: '理论正态分布'
        }];

        const layout = {
            title: {
                text: `残差分布直方图<br><sub>均值 = ${mean.toFixed(4)}, 标准差 = ${std.toFixed(4)}</sub>`,
                font: { size: 14 }
            },
            xaxis: {
                title: '残差值',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: '密度',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 70, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 渲染系数重要性图表（增强版）
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderCoefficientsImportanceChart(regression, uniqueId) {
        const chartId = `coefficients-importance-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        // 调用增强版系数重要性分析
        this.renderEnhancedCoefficientsImportanceChart(regression, uniqueId, chartElement);
    }

    /**
     * 渲染增强版系数重要性分析图表
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @param {HTMLElement} chartElement - 图表容器元素
     */
    renderEnhancedCoefficientsImportanceChart(regression, uniqueId, chartElement) {
        // 清空容器
        chartElement.innerHTML = '';

        // 创建主容器
        const mainContainer = document.createElement('div');
        mainContainer.className = 'enhanced-coefficients-container';

        // 添加模型拟合度信息面板
        const modelInfoPanel = this.createModelInfoPanel(regression);
        mainContainer.appendChild(modelInfoPanel);

        // 添加控制面板
        const controlPanel = this.createCoefficientsControlPanel(uniqueId);
        mainContainer.appendChild(controlPanel);

        // 添加图表容器
        const chartContainer = document.createElement('div');
        chartContainer.id = `enhanced-chart-container-${uniqueId}`;
        chartContainer.className = 'mt-4';
        mainContainer.appendChild(chartContainer);

        // 添加到页面
        chartElement.appendChild(mainContainer);

        // 初始化数据和状态
        this.initializeCoefficientsData(regression, uniqueId);

        // 渲染默认图表（增强水平条形图）
        this.renderCoefficientsBarChart(regression, uniqueId, 'absolute', 'all');
    }

    /**
     * 创建模型拟合度信息面板
     * @param {Object} regression - 回归分析结果
     * @returns {HTMLElement} 信息面板元素
     */
    createModelInfoPanel(regression) {
        const panel = document.createElement('div');
        panel.className = 'bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mb-4';

        const rSquared = regression.modelStats.rSquared;
        const adjustedRSquared = regression.modelStats.adjustedRSquared;
        const fStatistic = regression.modelStats.fStatistic;
        const fPValue = regression.modelStats.fPValue;
        const numVariables = regression.coefficients.variables.length;
        const significantVars = regression.coefficients.variables.filter(coef => coef.pValue < 0.05).length;

        panel.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h4 class="text-lg font-semibold text-blue-900 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    模型拟合度概览
                </h4>
                <div class="text-sm text-blue-700">
                    <span class="font-medium">${regression.yName}</span> 的预测模型
                </div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="bg-white rounded-lg p-3 border border-blue-100">
                    <div class="text-2xl font-bold text-blue-600">${(rSquared * 100).toFixed(1)}%</div>
                    <div class="text-sm text-gray-600">R² 决定系数</div>
                    <div class="text-xs text-gray-500 mt-1">解释变异程度</div>
                </div>

                <div class="bg-white rounded-lg p-3 border border-blue-100">
                    <div class="text-2xl font-bold text-indigo-600">${(adjustedRSquared * 100).toFixed(1)}%</div>
                    <div class="text-sm text-gray-600">调整R²</div>
                    <div class="text-xs text-gray-500 mt-1">校正后拟合度</div>
                </div>

                <div class="bg-white rounded-lg p-3 border border-blue-100">
                    <div class="text-2xl font-bold ${fPValue < 0.05 ? 'text-green-600' : 'text-red-600'}">${fStatistic.toFixed(2)}</div>
                    <div class="text-sm text-gray-600">F统计量</div>
                    <div class="text-xs text-gray-500 mt-1">p=${fPValue < 0.001 ? '<0.001' : fPValue.toFixed(3)}</div>
                </div>

                <div class="bg-white rounded-lg p-3 border border-blue-100">
                    <div class="text-2xl font-bold text-purple-600">${significantVars}/${numVariables}</div>
                    <div class="text-sm text-gray-600">显著变量</div>
                    <div class="text-xs text-gray-500 mt-1">p<0.05的变量数</div>
                </div>
            </div>
        `;

        return panel;
    }

    /**
     * 创建系数分析控制面板
     * @param {string} uniqueId - 唯一标识符
     * @returns {HTMLElement} 控制面板元素
     */
    createCoefficientsControlPanel(uniqueId) {
        const panel = document.createElement('div');
        panel.className = 'bg-white border border-gray-200 rounded-lg p-4';

        panel.innerHTML = `
            <div class="flex flex-wrap items-center justify-between gap-4">
                <!-- 图表类型选择 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">图表类型:</label>
                    <div class="flex bg-gray-100 rounded-lg p-1" id="chart-type-tabs-${uniqueId}">
                        <button class="chart-type-tab active px-3 py-1 text-sm font-medium rounded-md transition-colors"
                                data-type="bar" data-unique-id="${uniqueId}">
                            📊 条形图
                        </button>
                        <button class="chart-type-tab px-3 py-1 text-sm font-medium rounded-md transition-colors"
                                data-type="forest" data-unique-id="${uniqueId}">
                            🌲 森林图
                        </button>
                        <button class="chart-type-tab px-3 py-1 text-sm font-medium rounded-md transition-colors"
                                data-type="heatmap" data-unique-id="${uniqueId}">
                            🔥 热力图
                        </button>
                    </div>
                </div>

                <!-- 排序选择 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">排序方式:</label>
                    <select id="sort-option-${uniqueId}" class="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="absolute">按系数绝对值</option>
                        <option value="coefficient">按原始系数</option>
                        <option value="standardized">按标准化系数</option>
                        <option value="pvalue">按p值</option>
                        <option value="tvalue">按t值绝对值</option>
                        <option value="name">按变量名</option>
                    </select>
                </div>

                <!-- 显著性筛选 -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700">显著性筛选:</label>
                    <select id="significance-filter-${uniqueId}" class="px-3 py-1 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">显示全部</option>
                        <option value="0.05">仅显示 p<0.05</option>
                        <option value="0.01">仅显示 p<0.01</option>
                        <option value="0.001">仅显示 p<0.001</option>
                    </select>
                </div>
            </div>
        `;

        // 绑定事件监听器
        this.bindCoefficientsControlEvents(panel, uniqueId);

        return panel;
    }

    /**
     * 绑定系数分析控制面板事件
     * @param {HTMLElement} panel - 控制面板元素
     * @param {string} uniqueId - 唯一标识符
     */
    bindCoefficientsControlEvents(panel, uniqueId) {
        // 图表类型切换事件
        const chartTypeTabs = panel.querySelectorAll('.chart-type-tab');
        chartTypeTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                // 更新活动状态
                chartTypeTabs.forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');

                // 获取当前设置
                const chartType = e.target.dataset.type;
                const sortOption = document.getElementById(`sort-option-${uniqueId}`).value;
                const significanceFilter = document.getElementById(`significance-filter-${uniqueId}`).value;

                // 重新渲染图表
                this.renderCoefficientsChart(chartType, uniqueId, sortOption, significanceFilter);
            });
        });

        // 排序选择事件
        const sortSelect = panel.querySelector(`#sort-option-${uniqueId}`);
        sortSelect.addEventListener('change', (e) => {
            const activeTab = panel.querySelector('.chart-type-tab.active');
            const chartType = activeTab.dataset.type;
            const significanceFilter = document.getElementById(`significance-filter-${uniqueId}`).value;

            this.renderCoefficientsChart(chartType, uniqueId, e.target.value, significanceFilter);
        });

        // 显著性筛选事件
        const significanceSelect = panel.querySelector(`#significance-filter-${uniqueId}`);
        significanceSelect.addEventListener('change', (e) => {
            const activeTab = panel.querySelector('.chart-type-tab.active');
            const chartType = activeTab.dataset.type;
            const sortOption = document.getElementById(`sort-option-${uniqueId}`).value;

            this.renderCoefficientsChart(chartType, uniqueId, sortOption, e.target.value);
        });
    }

    /**
     * 初始化系数数据
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    initializeCoefficientsData(regression, uniqueId) {
        // 计算标准化系数
        const standardizedCoefficients = this.calculateStandardizedCoefficients(regression);

        // 准备完整的系数数据
        const coefficientsData = regression.coefficients.variables.map((coef, i) => ({
            ...coef,
            standardized: standardizedCoefficients[i],
            absEstimate: Math.abs(coef.estimate),
            absStandardized: Math.abs(standardizedCoefficients[i]),
            absTValue: Math.abs(coef.tValue),
            significanceLevel: this.getSignificanceLevel(coef.pValue),
            significanceStars: this.getSignificanceStars(coef.pValue),
            direction: coef.estimate > 0 ? 'positive' : 'negative',
            directionSymbol: coef.estimate > 0 ? '↗' : '↘',
            directionText: coef.estimate > 0 ? '正向影响' : '负向影响'
        }));

        // 存储数据供后续使用
        this[`coefficientsData_${uniqueId}`] = coefficientsData;
        this[`regressionData_${uniqueId}`] = regression;
    }

    /**
     * 获取显著性水平
     * @param {number} pValue - p值
     * @returns {string} 显著性水平
     */
    getSignificanceLevel(pValue) {
        if (pValue < 0.001) return 'highly_significant';
        if (pValue < 0.01) return 'significant';
        if (pValue < 0.05) return 'marginally_significant';
        return 'not_significant';
    }

    /**
     * 获取显著性星号
     * @param {number} pValue - p值
     * @returns {string} 星号标记
     */
    getSignificanceStars(pValue) {
        if (pValue < 0.001) return '***';
        if (pValue < 0.01) return '**';
        if (pValue < 0.05) return '*';
        return '';
    }

    /**
     * 渲染系数图表（根据类型）
     * @param {string} chartType - 图表类型
     * @param {string} uniqueId - 唯一标识符
     * @param {string} sortOption - 排序选项
     * @param {string} significanceFilter - 显著性筛选
     */
    renderCoefficientsChart(chartType, uniqueId, sortOption, significanceFilter) {
        switch (chartType) {
            case 'bar':
                this.renderCoefficientsBarChart(this[`regressionData_${uniqueId}`], uniqueId, sortOption, significanceFilter);
                break;
            case 'forest':
                this.renderCoefficientsForestPlot(this[`regressionData_${uniqueId}`], uniqueId, sortOption, significanceFilter);
                break;
            case 'heatmap':
                this.renderCoefficientsHeatmap(this[`regressionData_${uniqueId}`], uniqueId, sortOption, significanceFilter);
                break;
        }
    }

    /**
     * 计算标准化系数（Beta系数）
     * @param {Object} regression - 回归分析结果
     * @returns {Array<number>} 标准化系数数组
     */
    calculateStandardizedCoefficients(regression) {
        try {
            const yData = regression.data.y;
            const xData = regression.data.x;
            const coefficients = regression.coefficients.variables;

            // 计算因变量的标准差
            const yMean = yData.reduce((sum, val) => sum + val, 0) / yData.length;
            const yStd = Math.sqrt(yData.reduce((sum, val) => sum + Math.pow(val - yMean, 2), 0) / (yData.length - 1));

            // 计算每个自变量的标准差和标准化系数
            const standardizedCoefficients = [];

            for (let i = 0; i < coefficients.length; i++) {
                const xValues = xData[i];
                const xMean = xValues.reduce((sum, val) => sum + val, 0) / xValues.length;
                const xStd = Math.sqrt(xValues.reduce((sum, val) => sum + Math.pow(val - xMean, 2), 0) / (xValues.length - 1));

                // Beta = b * (Sx / Sy)
                const beta = coefficients[i].estimate * (xStd / yStd);
                standardizedCoefficients.push(beta);
            }

            return standardizedCoefficients;
        } catch (error) {
            // 标准化系数计算失败，使用原始系数
            return regression.coefficients.variables.map(coef => coef.estimate);
        }
    }

    /**
     * 渲染增强水平条形图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @param {string} sortOption - 排序选项
     * @param {string} significanceFilter - 显著性筛选
     */
    renderCoefficientsBarChart(regression, uniqueId, sortOption = 'absolute', significanceFilter = 'all') {
        const chartContainer = document.getElementById(`enhanced-chart-container-${uniqueId}`);
        if (!chartContainer) return;

        // 获取和处理数据
        const coefficientsData = this.getFilteredAndSortedCoefficients(uniqueId, sortOption, significanceFilter);
        if (coefficientsData.length === 0) {
            chartContainer.innerHTML = '<div class="text-center py-8 text-gray-500">没有符合筛选条件的变量</div>';
            return;
        }

        // 创建图表容器
        const chartDiv = document.createElement('div');
        chartDiv.id = `bar-chart-${uniqueId}`;
        chartDiv.className = 'w-full';
        chartContainer.innerHTML = '';
        chartContainer.appendChild(chartDiv);

        // 准备数据
        const variableNames = coefficientsData.map(coef => coef.name);
        const estimates = coefficientsData.map(coef => coef.estimate);
        const standardizedEstimates = coefficientsData.map(coef => coef.standardized);
        const pValues = coefficientsData.map(coef => coef.pValue);
        const tValues = coefficientsData.map(coef => coef.tValue);
        const confidenceIntervals = coefficientsData.map(coef => coef.confidenceInterval);

        // 增强的颜色方案（更高对比度）
        const colors = coefficientsData.map(coef => {
            switch (coef.significanceLevel) {
                case 'highly_significant': return 'rgba(5, 150, 105, 0.9)'; // 深绿色
                case 'significant': return 'rgba(29, 78, 216, 0.9)'; // 深蓝色
                case 'marginally_significant': return 'rgba(234, 88, 12, 0.9)'; // 深橙色
                default: return 'rgba(107, 114, 128, 0.7)'; // 深灰色
            }
        });

        // 计算误差条
        const errorX = estimates.map((est, i) => {
            const ci = confidenceIntervals[i];
            return [Math.abs(est - ci[0]), Math.abs(ci[1] - est)];
        });

        // 创建增强的变量标签
        const yLabels = variableNames.map((name, i) => {
            const coef = coefficientsData[i];
            return `${name} ${coef.directionSymbol}`;
        });

        // 创建系数值标签
        const textLabels = estimates.map((est, i) => {
            const stars = coefficientsData[i].significanceStars;
            return `${est.toFixed(3)}${stars}`;
        });

        const data = [{
            y: yLabels,
            x: estimates,
            type: 'bar',
            orientation: 'h',
            marker: {
                color: colors,
                line: {
                    color: 'rgba(255, 255, 255, 1)',
                    width: 2
                },
                opacity: 0.9
            },
            error_x: {
                type: 'data',
                symmetric: false,
                arrayminus: errorX.map(err => err[0]),
                arrayplus: errorX.map(err => err[1]),
                color: 'rgba(75, 85, 99, 0.9)',
                thickness: 4,
                width: 6
            },
            text: textLabels,
            textposition: 'auto',
            textfont: {
                color: 'white',
                size: 13,
                family: 'Arial, sans-serif',
                weight: 'bold'
            },
            hovertemplate:
                '<b>%{y}</b><br>' +
                '原始系数: %{x:.4f}<br>' +
                '标准化系数: %{customdata[0]:.4f}<br>' +
                't值: %{customdata[1]:.3f}<br>' +
                'p值: %{customdata[2]}<br>' +
                '95%置信区间: [%{customdata[3]:.3f}, %{customdata[4]:.3f}]<br>' +
                '影响方向: %{customdata[5]}<br>' +
                '显著性: %{customdata[6]}<br>' +
                '<extra></extra>',
            customdata: estimates.map((est, i) => [
                standardizedEstimates[i],
                tValues[i],
                pValues[i] < 0.001 ? '<0.001' : pValues[i].toFixed(3),
                confidenceIntervals[i][0],
                confidenceIntervals[i][1],
                coefficientsData[i].directionText,
                this.getSignificanceText(coefficientsData[i].significanceLevel)
            ]),
            name: '回归系数'
        }];

        // 添加零线
        data.push({
            x: [0, 0],
            y: [-0.5, variableNames.length - 0.5],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(75, 85, 99, 0.9)',
                dash: 'dash',
                width: 4
            },
            hoverinfo: 'skip',
            name: '零线'
        });

        // 动态计算图表尺寸
        const chartHeight = Math.max(450, variableNames.length * 70 + 200);
        const maxNameLength = Math.max(...variableNames.map(name => name.length));
        const leftMargin = Math.max(160, maxNameLength * 9 + 60);

        const layout = {
            title: {
                text: '系数重要性分析 - 增强条形图<br><sub>条形长度表示影响大小，颜色表示统计显著性，误差条表示95%置信区间</sub>',
                font: { size: 18, family: 'Arial, sans-serif', color: '#1f2937' },
                x: 0.5
            },
            xaxis: {
                title: {
                    text: '回归系数值',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                gridcolor: 'rgba(209, 213, 219, 0.8)',
                gridwidth: 2,
                zeroline: false,
                tickfont: { size: 13, color: '#4b5563' },
                showline: true,
                linecolor: 'rgba(107, 114, 128, 0.8)',
                linewidth: 2
            },
            yaxis: {
                title: {
                    text: '变量（↗正向影响 ↘负向影响）',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                gridcolor: 'rgba(209, 213, 219, 0.6)',
                gridwidth: 1,
                tickfont: { size: 13, family: 'Arial, sans-serif', color: '#4b5563' },
                automargin: true,
                showline: true,
                linecolor: 'rgba(107, 114, 128, 0.8)',
                linewidth: 2
            },
            showlegend: false,
            height: chartHeight,
            margin: { t: 100, r: 120, b: 80, l: leftMargin },
            plot_bgcolor: 'rgba(249, 250, 251, 0.95)',
            paper_bgcolor: 'white',
            annotations: this.createBarChartAnnotations(coefficientsData, estimates)
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false,
            toImageButtonOptions: {
                format: 'png',
                filename: `系数重要性分析_${regression.yName}`,
                height: chartHeight,
                width: 1200,
                scale: 2
            }
        };

        Plotly.newPlot(`bar-chart-${uniqueId}`, data, layout, config);

        // 添加图表说明
        this.addEnhancedCoefficientsExplanation(chartContainer, regression, coefficientsData);
    }

    /**
     * 获取筛选和排序后的系数数据
     * @param {string} uniqueId - 唯一标识符
     * @param {string} sortOption - 排序选项
     * @param {string} significanceFilter - 显著性筛选
     * @returns {Array} 处理后的系数数据
     */
    getFilteredAndSortedCoefficients(uniqueId, sortOption, significanceFilter) {
        let data = [...this[`coefficientsData_${uniqueId}`]];

        // 应用显著性筛选
        if (significanceFilter !== 'all') {
            const threshold = parseFloat(significanceFilter);
            data = data.filter(coef => coef.pValue < threshold);
        }

        // 应用排序
        switch (sortOption) {
            case 'absolute':
                data.sort((a, b) => b.absEstimate - a.absEstimate);
                break;
            case 'coefficient':
                data.sort((a, b) => b.estimate - a.estimate);
                break;
            case 'standardized':
                data.sort((a, b) => b.absStandardized - a.absStandardized);
                break;
            case 'pvalue':
                data.sort((a, b) => a.pValue - b.pValue);
                break;
            case 'tvalue':
                data.sort((a, b) => b.absTValue - a.absTValue);
                break;
            case 'name':
                data.sort((a, b) => a.name.localeCompare(b.name));
                break;
        }

        return data;
    }

    /**
     * 获取显著性文本描述
     * @param {string} significanceLevel - 显著性水平
     * @returns {string} 显著性描述
     */
    getSignificanceText(significanceLevel) {
        switch (significanceLevel) {
            case 'highly_significant': return '高度显著 (p<0.001)';
            case 'significant': return '显著 (p<0.01)';
            case 'marginally_significant': return '边际显著 (p<0.05)';
            default: return '不显著 (p≥0.05)';
        }
    }

    /**
     * 创建条形图注释
     * @param {Array} coefficientsData - 系数数据
     * @param {Array} estimates - 系数估计值
     * @returns {Array} 注释数组
     */
    createBarChartAnnotations(coefficientsData, estimates) {
        const annotations = [];

        // 显著性图例
        annotations.push({
            x: 1.02,
            y: 0.95,
            xref: 'paper',
            yref: 'paper',
            text: '<b>显著性图例</b>',
            showarrow: false,
            font: { size: 13, color: '#1f2937', family: 'Arial, sans-serif', weight: 'bold' },
            bgcolor: 'rgba(255, 255, 255, 0.95)',
            bordercolor: 'rgba(0, 0, 0, 0.2)',
            borderwidth: 1,
            align: 'left'
        });

        annotations.push({
            x: 1.02,
            y: 0.85,
            xref: 'paper',
            yref: 'paper',
            text: '🟢 *** p<0.001 (高度显著)<br>🔵 ** p<0.01 (显著)<br>🟠 * p<0.05 (边际显著)<br>⚫ 不显著',
            showarrow: false,
            font: { size: 11, color: '#374151', family: 'Arial, sans-serif' },
            bgcolor: 'rgba(255, 255, 255, 0.95)',
            bordercolor: 'rgba(0, 0, 0, 0.2)',
            borderwidth: 1,
            align: 'left'
        });

        // p值标签
        coefficientsData.forEach((coef, i) => {
            const pValueText = coef.pValue < 0.001 ? 'p<0.001' : `p=${coef.pValue.toFixed(3)}`;
            annotations.push({
                x: estimates[i] > 0 ? estimates[i] + Math.abs(estimates[i]) * 0.15 : estimates[i] - Math.abs(estimates[i]) * 0.15,
                y: i,
                text: pValueText,
                showarrow: false,
                font: { size: 10, color: '#6b7280', family: 'Arial, sans-serif' },
                bgcolor: 'rgba(255, 255, 255, 0.9)',
                bordercolor: 'rgba(0, 0, 0, 0.1)',
                borderwidth: 1
            });
        });

        return annotations;
    }

    /**
     * 添加增强的系数图表说明
     * @param {HTMLElement} chartContainer - 图表容器元素
     * @param {Object} regression - 回归分析结果
     * @param {Array} coefficientsData - 系数数据
     */
    addEnhancedCoefficientsExplanation(chartContainer, regression, coefficientsData) {
        const explanationContainer = document.createElement('div');
        explanationContainer.className = 'mt-6 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg';

        const significantCount = coefficientsData.filter(coef => coef.pValue < 0.05).length;
        const totalCount = coefficientsData.length;
        const mostImportantVar = coefficientsData.reduce((max, coef) =>
            Math.abs(coef.estimate) > Math.abs(max.estimate) ? coef : max
        );

        explanationContainer.innerHTML = `
            <div class="space-y-4">
                <h6 class="font-semibold text-blue-900 text-lg flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    📊 图表解读说明
                </h6>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 text-sm text-blue-800">
                    <div class="space-y-3">
                        <h7 class="font-medium text-blue-900 text-base">系数含义：</h7>
                        <ul class="space-y-2 list-disc list-inside">
                            <li><strong>原始系数</strong>：自变量变化1个单位时，因变量的平均变化量</li>
                            <li><strong>标准化系数</strong>：便于比较不同变量的相对重要性</li>
                            <li><strong>置信区间</strong>：系数真实值的可能范围（误差条）</li>
                            <li><strong>方向指示</strong>：↗表示正向影响，↘表示负向影响</li>
                        </ul>
                    </div>

                    <div class="space-y-3">
                        <h7 class="font-medium text-blue-900 text-base">显著性解读：</h7>
                        <ul class="space-y-2 list-disc list-inside">
                            <li><strong>*** (p<0.001)</strong>：高度显著，影响非常可靠</li>
                            <li><strong>** (p<0.01)</strong>：显著，影响比较可靠</li>
                            <li><strong>* (p<0.05)</strong>：边际显著，影响有一定可靠性</li>
                            <li><strong>无星号</strong>：不显著，影响不可靠</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-4 rounded-lg border border-blue-200">
                    <h7 class="font-medium text-blue-900 text-base">当前模型分析：</h7>
                    <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700">
                        <div class="space-y-2">
                            <p>• <strong>显著变量</strong>：${significantCount}/${totalCount} 个变量对${regression.yName}有显著影响</p>
                            <p>• <strong>最重要变量</strong>：${mostImportantVar.name}（系数=${mostImportantVar.estimate.toFixed(4)}）</p>
                        </div>
                        <div class="space-y-2">
                            <p>• <strong>影响方向</strong>：${mostImportantVar.directionText}</p>
                            <p>• <strong>实际意义</strong>：${mostImportantVar.name}每增加1个单位，${regression.yName}平均${mostImportantVar.estimate > 0 ? '增加' : '减少'}${Math.abs(mostImportantVar.estimate).toFixed(4)}个单位</p>
                        </div>
                    </div>
                </div>

                <div class="text-sm text-blue-600 italic bg-blue-100 p-3 rounded-lg">
                    💡 <strong>使用提示</strong>：关注显著性高且系数绝对值大的变量，它们对预测结果影响最大。可以使用上方的控制面板切换不同的图表类型和筛选条件。
                </div>
            </div>
        `;

        chartContainer.appendChild(explanationContainer);
    }

    /**
     * 渲染森林图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @param {string} sortOption - 排序选项
     * @param {string} significanceFilter - 显著性筛选
     */
    renderCoefficientsForestPlot(regression, uniqueId, sortOption = 'absolute', significanceFilter = 'all') {
        const chartContainer = document.getElementById(`enhanced-chart-container-${uniqueId}`);
        if (!chartContainer) return;

        // 获取和处理数据
        const coefficientsData = this.getFilteredAndSortedCoefficients(uniqueId, sortOption, significanceFilter);
        if (coefficientsData.length === 0) {
            chartContainer.innerHTML = '<div class="text-center py-8 text-gray-500">没有符合筛选条件的变量</div>';
            return;
        }

        // 创建图表容器
        const chartDiv = document.createElement('div');
        chartDiv.id = `forest-chart-${uniqueId}`;
        chartDiv.className = 'w-full';
        chartContainer.innerHTML = '';
        chartContainer.appendChild(chartDiv);

        // 准备森林图数据
        const variableNames = coefficientsData.map(coef => coef.name);
        const estimates = coefficientsData.map(coef => coef.estimate);
        const confidenceIntervals = coefficientsData.map(coef => coef.confidenceInterval);

        // 创建点的颜色和大小（基于显著性）
        const pointColors = coefficientsData.map(coef => {
            switch (coef.significanceLevel) {
                case 'highly_significant': return '#059669'; // 深绿色
                case 'significant': return '#1d4ed8'; // 深蓝色
                case 'marginally_significant': return '#ea580c'; // 深橙色
                default: return '#6b7280'; // 深灰色
            }
        });

        const pointSizes = coefficientsData.map(coef => {
            switch (coef.significanceLevel) {
                case 'highly_significant': return 16;
                case 'significant': return 14;
                case 'marginally_significant': return 12;
                default: return 10;
            }
        });

        const data = [];

        // 添加置信区间线条
        coefficientsData.forEach((coef, i) => {
            const ci = coef.confidenceInterval;
            data.push({
                x: [ci[0], ci[1]],
                y: [i, i],
                mode: 'lines',
                type: 'scatter',
                line: {
                    color: pointColors[i],
                    width: 4
                },
                hoverinfo: 'skip',
                showlegend: false,
                name: `CI_${coef.name}`
            });
        });

        // 添加系数点
        data.push({
            x: estimates,
            y: Array.from({ length: estimates.length }, (_, i) => i),
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: pointColors,
                size: pointSizes,
                line: {
                    color: 'white',
                    width: 2
                },
                symbol: 'diamond'
            },
            text: coefficientsData.map(coef => `${coef.name}: ${coef.estimate.toFixed(4)}${coef.significanceStars}`),
            hovertemplate:
                '<b>%{text}</b><br>' +
                '95%置信区间: [%{customdata[0]:.4f}, %{customdata[1]:.4f}]<br>' +
                'p值: %{customdata[2]}<br>' +
                '显著性: %{customdata[3]}<br>' +
                '<extra></extra>',
            customdata: coefficientsData.map(coef => [
                coef.confidenceInterval[0],
                coef.confidenceInterval[1],
                coef.pValue < 0.001 ? '<0.001' : coef.pValue.toFixed(3),
                this.getSignificanceText(coef.significanceLevel)
            ]),
            name: '系数估计'
        });

        // 添加零线
        data.push({
            x: [0, 0],
            y: [-0.5, variableNames.length - 0.5],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(75, 85, 99, 0.8)',
                dash: 'dash',
                width: 3
            },
            hoverinfo: 'skip',
            showlegend: false,
            name: '零线'
        });

        const chartHeight = Math.max(450, variableNames.length * 60 + 200);

        const layout = {
            title: {
                text: '系数重要性分析 - 森林图<br><sub>菱形表示系数估计值，横线表示95%置信区间，点的大小表示显著性</sub>',
                font: { size: 18, family: 'Arial, sans-serif', color: '#1f2937' },
                x: 0.5
            },
            xaxis: {
                title: {
                    text: '回归系数值',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                gridcolor: 'rgba(209, 213, 219, 0.8)',
                gridwidth: 2,
                zeroline: false,
                tickfont: { size: 13, color: '#4b5563' },
                showline: true,
                linecolor: 'rgba(107, 114, 128, 0.8)',
                linewidth: 2
            },
            yaxis: {
                title: {
                    text: '变量',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                tickmode: 'array',
                tickvals: Array.from({ length: variableNames.length }, (_, i) => i),
                ticktext: variableNames,
                tickfont: { size: 13, family: 'Arial, sans-serif', color: '#4b5563' },
                automargin: true,
                showline: true,
                linecolor: 'rgba(107, 114, 128, 0.8)',
                linewidth: 2
            },
            showlegend: false,
            height: chartHeight,
            margin: { t: 100, r: 120, b: 80, l: 200 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.95)',
            paper_bgcolor: 'white'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false,
            toImageButtonOptions: {
                format: 'png',
                filename: `系数森林图_${regression.yName}`,
                height: chartHeight,
                width: 1200,
                scale: 2
            }
        };

        Plotly.newPlot(`forest-chart-${uniqueId}`, data, layout, config);

        // 添加图表说明
        this.addEnhancedCoefficientsExplanation(chartContainer, regression, coefficientsData);
    }

    /**
     * 渲染系数热力图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     * @param {string} sortOption - 排序选项
     * @param {string} significanceFilter - 显著性筛选
     */
    renderCoefficientsHeatmap(regression, uniqueId, sortOption = 'absolute', significanceFilter = 'all') {
        const chartContainer = document.getElementById(`enhanced-chart-container-${uniqueId}`);
        if (!chartContainer) return;

        // 获取和处理数据
        const coefficientsData = this.getFilteredAndSortedCoefficients(uniqueId, sortOption, significanceFilter);
        if (coefficientsData.length === 0) {
            chartContainer.innerHTML = '<div class="text-center py-8 text-gray-500">没有符合筛选条件的变量</div>';
            return;
        }

        // 创建图表容器
        const chartDiv = document.createElement('div');
        chartDiv.id = `heatmap-chart-${uniqueId}`;
        chartDiv.className = 'w-full';
        chartContainer.innerHTML = '';
        chartContainer.appendChild(chartDiv);

        // 准备热力图数据
        const variableNames = coefficientsData.map(coef => coef.name);
        const metrics = ['系数值', '标准化系数', 't值', 'p值', '置信区间下限', '置信区间上限'];

        // 构建数据矩阵
        const zData = [];
        const textData = [];
        const hoverData = [];

        // 系数值行
        const coeffValues = coefficientsData.map(coef => coef.estimate);
        const coeffNormalized = this.normalizeValues(coeffValues);
        zData.push(coeffNormalized);
        textData.push(coefficientsData.map(coef => coef.estimate.toFixed(3)));
        hoverData.push(coefficientsData.map(coef => `系数值: ${coef.estimate.toFixed(4)}`));

        // 标准化系数行
        const stdCoeffValues = coefficientsData.map(coef => coef.standardized);
        const stdCoeffNormalized = this.normalizeValues(stdCoeffValues);
        zData.push(stdCoeffNormalized);
        textData.push(coefficientsData.map(coef => coef.standardized.toFixed(3)));
        hoverData.push(coefficientsData.map(coef => `标准化系数: ${coef.standardized.toFixed(4)}`));

        // t值行
        const tValues = coefficientsData.map(coef => coef.tValue);
        const tNormalized = this.normalizeValues(tValues);
        zData.push(tNormalized);
        textData.push(coefficientsData.map(coef => coef.tValue.toFixed(2)));
        hoverData.push(coefficientsData.map(coef => `t值: ${coef.tValue.toFixed(3)}`));

        // p值行（使用负对数变换以突出显著性）
        const pValues = coefficientsData.map(coef => -Math.log10(Math.max(coef.pValue, 1e-10)));
        const pNormalized = this.normalizeValues(pValues);
        zData.push(pNormalized);
        textData.push(coefficientsData.map(coef => coef.pValue < 0.001 ? '<0.001' : coef.pValue.toFixed(3)));
        hoverData.push(coefficientsData.map(coef => `p值: ${coef.pValue < 0.001 ? '<0.001' : coef.pValue.toFixed(4)}`));

        // 置信区间下限行
        const ciLowerValues = coefficientsData.map(coef => coef.confidenceInterval[0]);
        const ciLowerNormalized = this.normalizeValues(ciLowerValues);
        zData.push(ciLowerNormalized);
        textData.push(coefficientsData.map(coef => coef.confidenceInterval[0].toFixed(3)));
        hoverData.push(coefficientsData.map(coef => `置信区间下限: ${coef.confidenceInterval[0].toFixed(4)}`));

        // 置信区间上限行
        const ciUpperValues = coefficientsData.map(coef => coef.confidenceInterval[1]);
        const ciUpperNormalized = this.normalizeValues(ciUpperValues);
        zData.push(ciUpperNormalized);
        textData.push(coefficientsData.map(coef => coef.confidenceInterval[1].toFixed(3)));
        hoverData.push(coefficientsData.map(coef => `置信区间上限: ${coef.confidenceInterval[1].toFixed(4)}`));

        const data = [{
            z: zData,
            x: variableNames,
            y: metrics,
            type: 'heatmap',
            colorscale: [
                [0, '#f3f4f6'],      // 浅灰色
                [0.2, '#dbeafe'],    // 浅蓝色
                [0.4, '#93c5fd'],    // 中蓝色
                [0.6, '#3b82f6'],    // 蓝色
                [0.8, '#1d4ed8'],    // 深蓝色
                [1, '#1e40af']       // 最深蓝色
            ],
            text: textData,
            texttemplate: '%{text}',
            textfont: {
                size: 11,
                color: 'white',
                family: 'Arial, sans-serif'
            },
            hovertemplate:
                '<b>%{x}</b><br>' +
                '%{y}<br>' +
                '%{customdata}<br>' +
                '<extra></extra>',
            customdata: hoverData,
            showscale: true,
            colorbar: {
                title: {
                    text: '标准化强度',
                    font: { size: 12, family: 'Arial, sans-serif' }
                },
                tickfont: { size: 10 }
            }
        }];

        const chartHeight = Math.max(400, metrics.length * 80 + 200);

        const layout = {
            title: {
                text: '系数重要性分析 - 热力图<br><sub>颜色深度表示各指标的相对强度，数值显示具体值</sub>',
                font: { size: 18, family: 'Arial, sans-serif', color: '#1f2937' },
                x: 0.5
            },
            xaxis: {
                title: {
                    text: '变量',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                tickfont: { size: 12, color: '#4b5563' },
                tickangle: -45
            },
            yaxis: {
                title: {
                    text: '统计指标',
                    font: { size: 15, family: 'Arial, sans-serif', color: '#374151' }
                },
                tickfont: { size: 12, color: '#4b5563' }
            },
            height: chartHeight,
            margin: { t: 100, r: 120, b: 120, l: 150 },
            plot_bgcolor: 'white',
            paper_bgcolor: 'white'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false,
            toImageButtonOptions: {
                format: 'png',
                filename: `系数热力图_${regression.yName}`,
                height: chartHeight,
                width: 1200,
                scale: 2
            }
        };

        Plotly.newPlot(`heatmap-chart-${uniqueId}`, data, layout, config);

        // 添加图表说明
        this.addEnhancedCoefficientsExplanation(chartContainer, regression, coefficientsData);
    }

    /**
     * 标准化数值数组（用于热力图）
     * @param {Array<number>} values - 数值数组
     * @returns {Array<number>} 标准化后的数组
     */
    normalizeValues(values) {
        const min = Math.min(...values);
        const max = Math.max(...values);
        const range = max - min;

        if (range === 0) return values.map(() => 0.5);

        return values.map(val => (val - min) / range);
    }

    /**
     * 添加系数图表说明
     * @param {HTMLElement} chartElement - 图表容器元素
     * @param {Object} regression - 回归分析结果
     */
    addCoefficientsChartExplanation(chartElement, regression) {
        // 创建说明容器
        const explanationContainer = document.createElement('div');
        explanationContainer.className = 'mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg';

        // 计算一些统计信息
        const coefficients = regression.coefficients.variables;
        const significantCount = coefficients.filter(coef => coef.pValue < 0.05).length;
        const totalCount = coefficients.length;
        const maxAbsCoeff = Math.max(...coefficients.map(coef => Math.abs(coef.estimate)));
        const mostImportantVar = coefficients.find(coef => Math.abs(coef.estimate) === maxAbsCoeff);

        explanationContainer.innerHTML = `
            <div class="space-y-3">
                <h6 class="font-semibold text-blue-900 text-sm">📊 图表解读说明</h6>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
                    <div>
                        <h7 class="font-medium text-blue-900">系数含义：</h7>
                        <ul class="mt-1 space-y-1 list-disc list-inside">
                            <li><strong>原始系数</strong>：自变量变化1个单位时，因变量的平均变化量</li>
                            <li><strong>标准化系数</strong>：便于比较不同变量的相对重要性</li>
                            <li><strong>置信区间</strong>：系数真实值的可能范围（误差条）</li>
                        </ul>
                    </div>

                    <div>
                        <h7 class="font-medium text-blue-900">显著性解读：</h7>
                        <ul class="mt-1 space-y-1 list-disc list-inside">
                            <li><strong>*** (p<0.001)</strong>：高度显著，影响非常可靠</li>
                            <li><strong>** (p<0.01)</strong>：显著，影响比较可靠</li>
                            <li><strong>* (p<0.05)</strong>：边际显著，影响有一定可靠性</li>
                            <li><strong>无星号</strong>：不显著，影响不可靠</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-white p-3 rounded border border-blue-200">
                    <h7 class="font-medium text-blue-900">当前模型分析：</h7>
                    <div class="mt-2 space-y-1 text-sm text-blue-700">
                        <p>• <strong>显著变量</strong>：${significantCount}/${totalCount} 个变量对${regression.yName}有显著影响</p>
                        <p>• <strong>最重要变量</strong>：${mostImportantVar.name}（系数=${mostImportantVar.estimate.toFixed(4)}）</p>
                        <p>• <strong>影响方向</strong>：↗表示正向影响（变量增加，${regression.yName}增加），↘表示负向影响</p>
                        <p>• <strong>实际意义</strong>：${mostImportantVar.name}每增加1个单位，${regression.yName}平均${mostImportantVar.estimate > 0 ? '增加' : '减少'}${Math.abs(mostImportantVar.estimate).toFixed(4)}个单位</p>
                    </div>
                </div>

                <div class="text-xs text-blue-600 italic">
                    💡 提示：关注显著性高且系数绝对值大的变量，它们对预测结果影响最大
                </div>
            </div>
        `;

        // 将说明添加到图表容器的父元素
        const parentContainer = chartElement.closest('.bg-white');
        if (parentContainer) {
            parentContainer.appendChild(explanationContainer);
        }
    }

    /**
     * 渲染Cook距离图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderCookDistanceChart(regression, uniqueId) {
        const chartId = `cook-distance-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        // 计算Cook距离
        const cookDistances = this.calculateCookDistances(regression);
        const n = cookDistances.length;
        const p = regression.numVariables + 1; // 包括截距项

        // Cook距离阈值
        const threshold = 4 / n;
        const observationIndices = Array.from({ length: n }, (_, i) => i + 1);

        // 根据Cook距离设置颜色
        const colors = cookDistances.map(d => {
            if (d > threshold) return 'rgba(239, 68, 68, 0.8)'; // 红色：影响点
            if (d > threshold * 0.5) return 'rgba(245, 158, 11, 0.8)'; // 橙色：可疑点
            return 'rgba(59, 130, 246, 0.6)'; // 蓝色：正常点
        });

        const data = [{
            x: observationIndices,
            y: cookDistances,
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: colors,
                size: 8,
                line: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    width: 1
                }
            },
            text: cookDistances.map((d, i) =>
                `观测值 ${i + 1}<br>Cook距离: ${d.toFixed(4)}<br>${d > threshold ? '⚠️ 影响点' : '✅ 正常点'}`
            ),
            hovertemplate: '%{text}<extra></extra>',
            name: 'Cook距离'
        }, {
            x: [1, n],
            y: [threshold, threshold],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.8)',
                dash: 'dash',
                width: 2
            },
            hoverinfo: 'skip',
            name: '阈值线'
        }];

        const layout = {
            title: {
                text: `Cook距离图<br><sub>阈值 = ${threshold.toFixed(4)}</sub>`,
                font: { size: 14 }
            },
            xaxis: {
                title: '观测值序号',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: 'Cook距离',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 70, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white',
            annotations: [{
                x: 0.02,
                y: 0.98,
                xref: 'paper',
                yref: 'paper',
                text: `影响点数量: ${cookDistances.filter(d => d > threshold).length}`,
                showarrow: false,
                font: { size: 10, color: 'black' },
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: 'rgba(0, 0, 0, 0.1)',
                borderwidth: 1
            }]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 渲染杠杆值图
     * @param {Object} regression - 回归分析结果
     * @param {string} uniqueId - 唯一标识符
     */
    renderLeverageChart(regression, uniqueId) {
        const chartId = `leverage-chart-${uniqueId}`;
        const chartElement = document.getElementById(chartId);

        if (!chartElement) {
            console.warn(`图表容器 ${chartId} 未找到`);
            return;
        }

        // 计算杠杆值
        const leverageValues = this.calculateLeverageValues(regression);
        const n = leverageValues.length;
        const p = regression.numVariables + 1; // 包括截距项

        // 杠杆值阈值
        const threshold = 2 * p / n;
        const observationIndices = Array.from({ length: n }, (_, i) => i + 1);

        // 根据杠杆值设置颜色
        const colors = leverageValues.map(h => {
            if (h > threshold) return 'rgba(239, 68, 68, 0.8)'; // 红色：高杠杆点
            if (h > threshold * 0.7) return 'rgba(245, 158, 11, 0.8)'; // 橙色：中等杠杆点
            return 'rgba(59, 130, 246, 0.6)'; // 蓝色：正常点
        });

        const data = [{
            x: observationIndices,
            y: leverageValues,
            mode: 'markers',
            type: 'scatter',
            marker: {
                color: colors,
                size: 8,
                line: {
                    color: 'rgba(255, 255, 255, 0.8)',
                    width: 1
                }
            },
            text: leverageValues.map((h, i) =>
                `观测值 ${i + 1}<br>杠杆值: ${h.toFixed(4)}<br>${h > threshold ? '⚠️ 高杠杆点' : '✅ 正常点'}`
            ),
            hovertemplate: '%{text}<extra></extra>',
            name: '杠杆值'
        }, {
            x: [1, n],
            y: [threshold, threshold],
            mode: 'lines',
            type: 'scatter',
            line: {
                color: 'rgba(239, 68, 68, 0.8)',
                dash: 'dash',
                width: 2
            },
            hoverinfo: 'skip',
            name: '阈值线'
        }];

        const layout = {
            title: {
                text: `杠杆值图<br><sub>阈值 = ${threshold.toFixed(4)}</sub>`,
                font: { size: 14 }
            },
            xaxis: {
                title: '观测值序号',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            yaxis: {
                title: '杠杆值',
                gridcolor: 'rgba(229, 231, 235, 0.8)'
            },
            showlegend: false,
            margin: { t: 70, r: 30, b: 50, l: 60 },
            plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
            paper_bgcolor: 'white',
            annotations: [{
                x: 0.02,
                y: 0.98,
                xref: 'paper',
                yref: 'paper',
                text: `高杠杆点数量: ${leverageValues.filter(h => h > threshold).length}`,
                showarrow: false,
                font: { size: 10, color: 'black' },
                bgcolor: 'rgba(255, 255, 255, 0.8)',
                bordercolor: 'rgba(0, 0, 0, 0.1)',
                borderwidth: 1
            }]
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],
            displaylogo: false
        };

        Plotly.newPlot(chartId, data, layout, config);
    }

    /**
     * 正态分布逆函数近似
     * @param {number} p - 概率值
     * @returns {number} 对应的z值
     */
    normalInverse(p) {
        // 使用Beasley-Springer-Moro算法的简化版本
        if (p <= 0 || p >= 1) {
            throw new Error('概率值必须在0和1之间');
        }

        if (p < 0.5) {
            return -this.normalInverse(1 - p);
        }

        const t = Math.sqrt(-2 * Math.log(1 - p));
        return t - (2.30753 + 0.27061 * t) / (1 + 0.99229 * t + 0.04481 * t * t);
    }

    /**
     * 计算相关系数
     * @param {Array<number>} x - x数组
     * @param {Array<number>} y - y数组
     * @returns {number} 相关系数
     */
    calculateCorrelation(x, y) {
        const n = x.length;
        const meanX = x.reduce((sum, val) => sum + val, 0) / n;
        const meanY = y.reduce((sum, val) => sum + val, 0) / n;

        let numerator = 0;
        let sumXSquared = 0;
        let sumYSquared = 0;

        for (let i = 0; i < n; i++) {
            const deltaX = x[i] - meanX;
            const deltaY = y[i] - meanY;
            numerator += deltaX * deltaY;
            sumXSquared += deltaX * deltaX;
            sumYSquared += deltaY * deltaY;
        }

        const denominator = Math.sqrt(sumXSquared * sumYSquared);
        return denominator === 0 ? 0 : numerator / denominator;
    }

    /**
     * 计算Cook距离
     * @param {Object} regression - 回归分析结果
     * @returns {Array<number>} Cook距离数组
     */
    calculateCookDistances(regression) {
        const n = regression.n;
        const p = regression.numVariables + 1; // 包括截距项
        const residuals = regression.data.residuals;
        const fitted = regression.data.fitted;
        const MSE = regression.modelSummary.standardError ** 2;

        // 简化的Cook距离计算（基于残差和杠杆值）
        const leverageValues = this.calculateLeverageValues(regression);
        const standardizedResiduals = residuals.map(r => r / regression.modelSummary.standardError);

        return standardizedResiduals.map((stdRes, i) => {
            const h = leverageValues[i];
            return (stdRes * stdRes / p) * (h / (1 - h));
        });
    }

    /**
     * 计算杠杆值
     * @param {Object} regression - 回归分析结果
     * @returns {Array<number>} 杠杆值数组
     */
    calculateLeverageValues(regression) {
        const n = regression.n;
        const p = regression.numVariables + 1; // 包括截距项

        // 构建设计矩阵X
        const X = [];
        for (let i = 0; i < n; i++) {
            const row = [1]; // 截距项
            for (let j = 0; j < regression.numVariables; j++) {
                row.push(regression.data.x[j][i]);
            }
            X.push(row);
        }

        try {
            // 计算 X(X'X)^(-1)X' 的对角元素
            const XtX = this.matrixMultiply(this.matrixTranspose(X), X);
            const XtXInverse = this.matrixInverse(XtX);
            const leverageValues = [];

            for (let i = 0; i < n; i++) {
                let leverage = 0;
                for (let j = 0; j < p; j++) {
                    for (let k = 0; k < p; k++) {
                        leverage += X[i][j] * XtXInverse[j][k] * X[i][k];
                    }
                }
                leverageValues.push(leverage);
            }

            return leverageValues;
        } catch (error) {
            // 杠杆值计算失败，使用近似值
            // 返回近似杠杆值
            return Array(n).fill(p / n);
        }
    }

    /**
     * 矩阵转置
     * @param {Array<Array<number>>} matrix - 输入矩阵
     * @returns {Array<Array<number>>} 转置矩阵
     */
    matrixTranspose(matrix) {
        const rows = matrix.length;
        const cols = matrix[0].length;
        const result = [];

        for (let j = 0; j < cols; j++) {
            result[j] = [];
            for (let i = 0; i < rows; i++) {
                result[j][i] = matrix[i][j];
            }
        }

        return result;
    }

    /**
     * 矩阵乘法
     * @param {Array<Array<number>>} A - 矩阵A
     * @param {Array<Array<number>>} B - 矩阵B
     * @returns {Array<Array<number>>} 乘积矩阵
     */
    matrixMultiply(A, B) {
        const rowsA = A.length;
        const colsA = A[0].length;
        const colsB = B[0].length;
        const result = [];

        for (let i = 0; i < rowsA; i++) {
            result[i] = [];
            for (let j = 0; j < colsB; j++) {
                let sum = 0;
                for (let k = 0; k < colsA; k++) {
                    sum += A[i][k] * B[k][j];
                }
                result[i][j] = sum;
            }
        }

        return result;
    }

    /**
     * 矩阵求逆（使用高斯-约旦消元法）
     * @param {Array<Array<number>>} matrix - 输入矩阵
     * @returns {Array<Array<number>>} 逆矩阵
     */
    matrixInverse(matrix) {
        const n = matrix.length;

        // 创建增广矩阵 [A|I]
        const augmented = [];
        for (let i = 0; i < n; i++) {
            augmented[i] = [...matrix[i]];
            for (let j = 0; j < n; j++) {
                augmented[i][n + j] = i === j ? 1 : 0;
            }
        }

        // 高斯-约旦消元
        for (let i = 0; i < n; i++) {
            // 寻找主元
            let maxRow = i;
            for (let k = i + 1; k < n; k++) {
                if (Math.abs(augmented[k][i]) > Math.abs(augmented[maxRow][i])) {
                    maxRow = k;
                }
            }

            // 交换行
            if (maxRow !== i) {
                [augmented[i], augmented[maxRow]] = [augmented[maxRow], augmented[i]];
            }

            // 检查奇异矩阵
            if (Math.abs(augmented[i][i]) < 1e-10) {
                throw new Error('矩阵奇异，无法求逆');
            }

            // 将主元归一化
            const pivot = augmented[i][i];
            for (let j = 0; j < 2 * n; j++) {
                augmented[i][j] /= pivot;
            }

            // 消元
            for (let k = 0; k < n; k++) {
                if (k !== i) {
                    const factor = augmented[k][i];
                    for (let j = 0; j < 2 * n; j++) {
                        augmented[k][j] -= factor * augmented[i][j];
                    }
                }
            }
        }

        // 提取逆矩阵
        const inverse = [];
        for (let i = 0; i < n; i++) {
            inverse[i] = augmented[i].slice(n);
        }

        return inverse;
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        this.fileDropZone.innerHTML = `
            <div class="space-y-4">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                <p class="text-lg text-gray-600">正在处理文件...</p>
            </div>
        `;
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        this.fileDropZone.innerHTML = `
            <div class="space-y-4">
                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div>
                    <p class="text-lg text-gray-600">拖拽Excel文件到此处，或</p>
                    <button id="file-select-btn" class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                        选择文件
                    </button>
                    <input type="file" id="file-input" accept=".xlsx,.xls" class="hidden">
                </div>
                <p class="text-sm text-gray-500">支持 .xlsx 和 .xls 格式</p>
            </div>
        `;
        
        // 重新绑定事件
        this.initializeElements();
        this.bindEvents();
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     */
    showError(message) {
        alert('错误: ' + message);
    }

    /**
     * 创建结果区域
     * @param {string} title - 区域标题
     * @returns {HTMLElement} 结果容器
     */
    createResultSection(title) {
        const section = document.createElement('div');
        section.className = 'mb-4';

        const titleElement = document.createElement('h3');
        titleElement.className = 'text-lg font-semibold text-gray-900 mb-3 border-b pb-1';
        titleElement.textContent = title;

        section.appendChild(titleElement);
        this.resultsContainer.appendChild(section);

        return section;
    }

    /**
     * 创建统计表格
     * @param {string} columnName - 列名
     * @param {Object} stats - 统计数据
     * @returns {HTMLElement} 表格元素
     */
    createStatsTable(columnName, stats) {
        const container = document.createElement('div');
        container.className = 'compact-card mb-3';

        const title = document.createElement('h4');
        title.className = 'font-medium text-gray-900 mb-2';
        title.textContent = `${columnName} - 描述统计`;

        const table = document.createElement('table');
        table.className = 'compact-table w-full';

        const statsData = [
            ['样本量', stats.count],
            ['均值', stats.mean.toFixed(4)],
            ['中位数', stats.median.toFixed(4)],
            ['标准差', stats.standardDeviation.toFixed(4)],
            ['方差', stats.variance.toFixed(4)],
            ['最小值', stats.min.toFixed(4)],
            ['最大值', stats.max.toFixed(4)],
            ['范围', stats.range.toFixed(4)],
            ['第一四分位数', stats.quartiles.q1.toFixed(4)],
            ['第三四分位数', stats.quartiles.q3.toFixed(4)],
            ['偏度', stats.skewness.toFixed(4)],
            ['峰度', stats.kurtosis.toFixed(4)]
        ];

        statsData.forEach(([label, value]) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="font-medium text-gray-700">${label}</td>
                <td class="text-gray-900">${value}</td>
            `;
            table.appendChild(row);
        });

        container.appendChild(title);
        container.appendChild(table);

        return container;
    }

    /**
     * 创建水平统计表格
     * @param {Array} columns - 列名数组
     * @param {Array} allStats - 所有变量的统计数据
     * @returns {HTMLElement} 水平统计表格
     */
    createHorizontalStatsTable(columns, allStats) {
        const container = document.createElement('div');
        container.className = 'compact-card mb-3';

        const title = document.createElement('h4');
        title.className = 'font-medium text-gray-900 mb-2';
        title.textContent = '描述统计汇总';

        const tableContainer = document.createElement('div');
        tableContainer.className = 'stats-table-container';

        const table = document.createElement('table');
        table.className = 'horizontal-stats-table';

        // 定义统计指标
        const statLabels = [
            { key: 'count', label: '样本量', format: (val) => val },
            { key: 'mean', label: '均值', format: (val) => val.toFixed(2) },
            { key: 'median', label: '中位数', format: (val) => val.toFixed(2) },
            { key: 'standardDeviation', label: '标准差', format: (val) => val.toFixed(2) },
            { key: 'variance', label: '方差', format: (val) => val.toFixed(2) },
            { key: 'min', label: '最小值', format: (val) => val.toFixed(2) },
            { key: 'max', label: '最大值', format: (val) => val.toFixed(2) },
            { key: 'range', label: '范围', format: (val) => val.toFixed(2) },
            { key: 'quartiles.q1', label: 'Q1', format: (val) => val.toFixed(2) },
            { key: 'quartiles.q3', label: 'Q3', format: (val) => val.toFixed(2) },
            { key: 'skewness', label: '偏度', format: (val) => val.toFixed(2) },
            { key: 'kurtosis', label: '峰度', format: (val) => val.toFixed(2) }
        ];

        // 创建表头
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');

        // 第一列：变量名
        const variableHeader = document.createElement('th');
        variableHeader.className = 'variable-name';
        variableHeader.textContent = '变量';
        headerRow.appendChild(variableHeader);

        // 统计指标列
        statLabels.forEach(stat => {
            const th = document.createElement('th');
            th.textContent = stat.label;
            headerRow.appendChild(th);
        });

        thead.appendChild(headerRow);
        table.appendChild(thead);

        // 创建表体
        const tbody = document.createElement('tbody');

        columns.forEach((column, index) => {
            const stats = allStats[index];
            const row = document.createElement('tr');

            // 变量名列
            const variableCell = document.createElement('td');
            variableCell.className = 'variable-name';
            variableCell.textContent = column;
            row.appendChild(variableCell);

            // 统计值列
            statLabels.forEach(stat => {
                const cell = document.createElement('td');

                // 获取嵌套属性值
                let value = stats;
                const keys = stat.key.split('.');
                for (const key of keys) {
                    value = value[key];
                }

                cell.textContent = stat.format(value);
                row.appendChild(cell);
            });

            tbody.appendChild(row);
        });

        table.appendChild(tbody);
        tableContainer.appendChild(table);
        container.appendChild(title);
        container.appendChild(tableContainer);

        return container;
    }







    /**
     * 清空分析结果
     */
    clearResults() {
        this.resultsContainer.innerHTML = '';
        this.analysisResults.classList.add('hidden');

        // 销毁所有图表
        window.chartRenderer.destroyAllCharts();

        // 显示清空成功消息
        const message = document.createElement('div');
        message.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
        message.textContent = '结果已清空';
        document.body.appendChild(message);

        setTimeout(() => {
            document.body.removeChild(message);
        }, 2000);
    }

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 切换解释区域的显示/隐藏
     * @param {string} elementId - 解释区域的ID
     */
    toggleExplanation(elementId) {
        const element = document.getElementById(elementId);

        if (element) {
            element.classList.toggle('hidden');

            // 更新按钮文本
            const isHidden = element.classList.contains('hidden');
            const button = document.querySelector(`[data-target="${elementId}"]`);

            if (button) {
                const newText = isHidden ? '📖 查看解释' : '📖 收起解释';
                button.innerHTML = newText;
            }
        }
    }

    /**
     * R²解释
     * @param {number} rSquared - R²值
     * @returns {string} 解释文本
     */
    getRSquaredInterpretation(rSquared) {
        const percentage = (rSquared * 100).toFixed(1);
        if (rSquared >= 0.8) {
            return `优秀：解释了${percentage}%的变异`;
        } else if (rSquared >= 0.6) {
            return `良好：解释了${percentage}%的变异`;
        } else if (rSquared >= 0.4) {
            return `一般：解释了${percentage}%的变异`;
        } else {
            return `较弱：仅解释了${percentage}%的变异`;
        }
    }

    /**
     * 调整R²解释
     * @param {number} adjustedRSquared - 调整R²值
     * @returns {string} 解释文本
     */
    getAdjustedRSquaredInterpretation(adjustedRSquared) {
        const percentage = (adjustedRSquared * 100).toFixed(1);
        return `考虑样本量后：${percentage}%`;
    }

    /**
     * 标准误解释
     * @param {number} standardError - 标准误值
     * @returns {string} 解释文本
     */
    getStandardErrorInterpretation(standardError) {
        if (standardError < 1) {
            return '预测误差较小';
        } else if (standardError < 5) {
            return '预测误差适中';
        } else {
            return '预测误差较大';
        }
    }

    /**
     * F统计量解释
     * @param {number} fStatistic - F统计量值
     * @param {number} pValue - p值
     * @returns {string} 解释文本
     */
    getFStatisticInterpretation(_, pValue) {
        if (pValue < 0.001) {
            return '模型高度显著';
        } else if (pValue < 0.01) {
            return '模型很显著';
        } else if (pValue < 0.05) {
            return '模型显著';
        } else {
            return '模型不显著';
        }
    }

    /**
     * p值解释
     * @param {number} pValue - p值
     * @returns {string} 解释文本
     */
    getPValueInterpretation(pValue) {
        if (pValue < 0.001) {
            return '极其显著 ***';
        } else if (pValue < 0.01) {
            return '很显著 **';
        } else if (pValue < 0.05) {
            return '显著 *';
        } else if (pValue < 0.1) {
            return '边缘显著 .';
        } else {
            return '不显著';
        }
    }

    /**
     * 模型摘要详细解释
     * @param {Object} regression - 回归分析结果
     * @returns {string} 详细解释HTML
     */
    getModelSummaryDetailedExplanation(regression) {
        const rSquaredPercent = (regression.modelSummary.rSquared * 100).toFixed(1);
        return `
            <h6 class="font-medium text-blue-900 mb-2">📊 模型摘要指标详解</h6>
            <div class="space-y-2 text-blue-800">
                <p><strong>R²（决定系数）</strong>：表示模型能解释多少比例的数据变异。${rSquaredPercent}%意味着自变量能解释因变量${rSquaredPercent}%的变化。</p>
                <p><strong>调整R²</strong>：考虑了样本量和变量数量的影响，比R²更准确。</p>
                <p><strong>标准误</strong>：预测值的平均误差大小，数值越小预测越准确。</p>
                <p><strong>F统计量</strong>：检验模型整体是否有意义，配合p值判断模型显著性。</p>
                <p><strong>判断标准</strong>：R²>0.8为优秀，0.6-0.8为良好，0.4-0.6为一般，<0.4为较弱；p值<0.05表示显著。</p>
            </div>
        `;
    }

    /**
     * 截距解释
     * @param {number} intercept - 截距值
     * @param {string} yName - 因变量名称
     * @returns {string} 解释文本
     */
    getInterceptInterpretation(intercept, yName) {
        return `当自变量为0时，${yName}的预期值为${intercept.toFixed(2)}`;
    }

    /**
     * 斜率解释
     * @param {number} slope - 斜率值
     * @param {string} xName - 自变量名称
     * @param {string} yName - 因变量名称
     * @returns {string} 解释文本
     */
    getSlopeInterpretation(slope, xName, yName) {
        const direction = slope > 0 ? '增加' : '减少';
        const absSlope = Math.abs(slope);
        return `${xName}每增加1个单位，${yName}平均${direction}${absSlope.toFixed(2)}个单位`;
    }

    /**
     * 系数详细解释
     * @param {Object} regression - 回归分析结果
     * @returns {string} 详细解释HTML
     */
    getCoefficientsDetailedExplanation(regression) {
        const slopeValue = regression.coefficients.slope.estimate;
        const direction = slopeValue > 0 ? '正向' : '负向';
        const strength = Math.abs(slopeValue) > 1 ? '较强' : '较弱';

        return `
            <h6 class="font-medium text-blue-900 mb-2">📈 系数汇总指标详解</h6>
            <div class="space-y-2 text-blue-800">
                <p><strong>估计值</strong>：回归系数的数值，表示变量间的关系强度和方向。</p>
                <p><strong>标准误</strong>：估计值的不确定性，数值越小估计越精确。</p>
                <p><strong>t值</strong>：检验系数是否显著不为0的统计量，绝对值越大越显著。</p>
                <p><strong>p值</strong>：显著性概率，<0.05表示显著，<0.01表示很显著，<0.001表示极显著。</p>
                <p><strong>95%置信区间</strong>：真实系数值有95%的概率落在此区间内。</p>
                <p><strong>本模型解释</strong>：${regression.xName}对${regression.yName}有${direction}影响，影响强度${strength}。</p>
            </div>
        `;
    }

    /**
     * ANOVA详细解释
     * @param {Object} regression - 回归分析结果
     * @returns {string} 详细解释HTML
     */
    getAnovaDetailedExplanation(regression) {
        const rSquared = regression.modelSummary.rSquared;
        const explainedPercent = (rSquared * 100).toFixed(1);
        const unexplainedPercent = ((1 - rSquared) * 100).toFixed(1);

        return `
            <h6 class="font-medium text-blue-900 mb-2">📊 方差分析详解</h6>
            <div class="space-y-2 text-blue-800">
                <p><strong>方差分析的作用</strong>：将数据的总变异分解为"模型能解释的"和"模型无法解释的"两部分。</p>
                <p><strong>回归平方和</strong>：模型能解释的变异，占总变异的${explainedPercent}%。</p>
                <p><strong>残差平方和</strong>：模型无法解释的变异，占总变异的${unexplainedPercent}%。</p>
                <p><strong>F检验</strong>：检验模型整体是否显著，F值越大、p值越小，模型越显著。</p>
                <p><strong>自由度</strong>：回归自由度=变量个数，残差自由度=样本量-变量个数-1。</p>
                <p><strong>判断标准</strong>：F检验p值<0.05表示模型整体显著，有统计学意义。</p>
            </div>
        `;
    }

    /**
     * 残差分析详细解释
     * @param {Object} regression - 回归分析结果
     * @returns {string} 详细解释HTML
     */
    getResidualDetailedExplanation(regression) {
        const meanResidual = Math.abs(regression.residualAnalysis.statistics.mean);
        const residualQuality = meanResidual < 0.001 ? '优秀' : meanResidual < 0.01 ? '良好' : '需要改进';

        return `
            <h6 class="font-medium text-blue-900 mb-2">🔍 残差分析详解</h6>
            <div class="space-y-2 text-blue-800">
                <p><strong>残差的含义</strong>：残差 = 实际值 - 预测值，反映模型预测的准确性。</p>
                <p><strong>残差均值</strong>：理想情况下应该接近0，当前为${regression.residualAnalysis.statistics.mean.toFixed(6)}（${residualQuality}）。</p>
                <p><strong>残差分布</strong>：应该随机分布，无明显模式，且接近正态分布。</p>
                <p><strong>异常值检测</strong>：标准化残差绝对值>2的点可能是异常值，需要特别关注。</p>
                <p><strong>图表解读</strong>：
                  <br>• 残差vs拟合值图：点应随机分布，无明显趋势
                  <br>• Q-Q图：点应接近对角线，表示正态分布
                  <br>• 标准化残差图：大部分点应在±2之间</p>
            </div>
        `;
    }











    /**
     * 生成个性化的回归结果解释
     * @param {Object} regression - 回归分析结果
     * @returns {string} 个性化解释HTML
     */
    generatePersonalizedInterpretation(regression) {
        const xName = regression.xName;
        const yName = regression.yName;
        const slope = regression.coefficients.slope.estimate;
        const rSquared = regression.modelSummary.rSquared;
        const pValue = regression.coefficients.slope.pValue;

        const direction = slope > 0 ? '正向' : '负向';
        const significance = pValue < 0.001 ? '极其显著' : pValue < 0.01 ? '很显著' : pValue < 0.05 ? '显著' : '不显著';
        const rSquaredPercent = (rSquared * 100).toFixed(1);
        const slopeAbs = Math.abs(slope);

        let strengthDescription = '';
        if (slopeAbs < 0.1) {
            strengthDescription = '影响很小';
        } else if (slopeAbs < 0.5) {
            strengthDescription = '影响较小';
        } else if (slopeAbs < 1) {
            strengthDescription = '影响适中';
        } else if (slopeAbs < 2) {
            strengthDescription = '影响较大';
        } else {
            strengthDescription = '影响很大';
        }

        let qualityAssessment = '';
        if (rSquared >= 0.8) {
            qualityAssessment = '模型拟合效果优秀';
        } else if (rSquared >= 0.6) {
            qualityAssessment = '模型拟合效果良好';
        } else if (rSquared >= 0.4) {
            qualityAssessment = '模型拟合效果一般';
        } else {
            qualityAssessment = '模型拟合效果较差';
        }

        return `
            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h6 class="font-medium text-green-900 mb-3">🎯 个性化结果解释</h6>
                <div class="space-y-2 text-green-800">
                    <p><strong>关系方向</strong>：${xName}与${yName}之间存在${direction}关系（${significance}）。</p>
                    <p><strong>影响程度</strong>：${xName}每增加1个单位，${yName}平均${slope > 0 ? '增加' : '减少'}${slopeAbs.toFixed(3)}个单位（${strengthDescription}）。</p>
                    <p><strong>解释能力</strong>：${xName}能够解释${yName}变化的${rSquaredPercent}%（${qualityAssessment}）。</p>
                    <p><strong>统计显著性</strong>：该关系在统计学上${significance}（p${pValue < 0.001 ? '<0.001' : '=' + pValue.toFixed(3)}）。</p>
                    ${this.generateBusinessInterpretation(xName, yName, slope, rSquared)}
                </div>
            </div>
        `;
    }

    /**
     * 生成业务解释
     * @param {string} xName - 自变量名称
     * @param {string} yName - 因变量名称
     * @param {number} slope - 斜率
     * @param {number} rSquared - R²值
     * @returns {string} 业务解释
     */
    generateBusinessInterpretation(xName, yName, slope, rSquared) {
        // 根据变量名称生成更具体的业务解释
        const xLower = xName.toLowerCase();
        const yLower = yName.toLowerCase();

        let businessContext = '';

        // 身高体重关系
        if ((xLower.includes('身高') || xLower.includes('height')) &&
            (yLower.includes('体重') || yLower.includes('weight'))) {
            businessContext = `<p><strong>实际意义</strong>：身高每增加1cm，体重平均增加${(slope * 1).toFixed(2)}kg。这符合人体生理规律。</p>`;
        }
        // 年龄收入关系
        else if ((xLower.includes('年龄') || xLower.includes('age')) &&
                 (yLower.includes('收入') || yLower.includes('income') || yLower.includes('salary'))) {
            businessContext = `<p><strong>实际意义</strong>：年龄每增加1岁，收入平均${slope > 0 ? '增加' : '减少'}${Math.abs(slope).toFixed(2)}元。这可能反映了工作经验的价值。</p>`;
        }
        // 教育程度收入关系
        else if ((xLower.includes('教育') || xLower.includes('education')) &&
                 (yLower.includes('收入') || yLower.includes('income'))) {
            businessContext = `<p><strong>实际意义</strong>：教育程度的提升对收入有${slope > 0 ? '正面' : '负面'}影响，体现了教育投资的回报。</p>`;
        }
        // 通用解释
        else {
            businessContext = `<p><strong>实际意义</strong>：在实际应用中，可以利用${xName}来预测${yName}，预测准确度约为${(rSquared * 100).toFixed(1)}%。</p>`;
        }

        return businessContext;
    }





    /**
     * 获取数据类型图标
     * @param {string} dataType - 数据类型
     * @returns {string} 对应的图标
     */
    getDataTypeIcon(dataType) {
        const iconMap = {
            'numeric': '🔢',
            'text': '📝',
            'date': '📅',
            'boolean': '✅',
            'mixed': '🔀',
            'empty': '❌'
        };
        return iconMap[dataType] || '❓';
    }

    /**
     * 获取质量评分对应的CSS类
     * @param {number} score - 质量评分
     * @returns {string} CSS类名
     */
    getQualityScoreClass(score) {
        if (score >= 80) return 'quality-excellent';
        if (score >= 60) return 'quality-good';
        if (score >= 40) return 'quality-fair';
        return 'quality-poor';
    }
}

// 全局解释切换函数
window.toggleExplanationGlobal = function(elementId) {
    const element = document.getElementById(elementId);

    if (element) {
        element.classList.toggle('hidden');

        // 更新按钮文本
        const isHidden = element.classList.contains('hidden');
        const button = document.querySelector(`[data-target="${elementId}"]`);

        if (button) {
            const newText = isHidden ? '📖 查看解释' : '📖 收起解释';
            button.innerHTML = newText;
        }
    }
};

// 当页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new StatisticsApp();
});
